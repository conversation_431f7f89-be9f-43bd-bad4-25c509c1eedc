import { format } from 'date-fns';
export const formatTimeAsDHM = (timeInMinutes: number | null | undefined): string => {
	if (!timeInMinutes) return '';

	const days = Math.floor(timeInMinutes / (24 * 60));
	const hours = Math.floor((timeInMinutes % (24 * 60)) / 60);
	const minutes = Math.floor(timeInMinutes % 60);

	return `${days}d : ${hours.toString().padStart(2, '0')}h: ${minutes.toString().padStart(2, '0')}m`;
};
// Function to format amount with two decimal places
export function formatAmount(amount: number): string {
	return new Intl.NumberFormat('en-US', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	}).format(amount);
}

// Function to format amount with no decimal places
export function formatAmountSimple(amount: number): string {
	return new Intl.NumberFormat('en-US', {
		minimumFractionDigits: 0,
		maximumFractionDigits: 0
	}).format(amount);
}

// Function to format quantity with 0 to 2 decimal places
export function formatQty(qty: number): string {
	return new Intl.NumberFormat('en-US', {
		minimumFractionDigits: 0,
		maximumFractionDigits: 2
	}).format(qty);
}

// Function to format total hours into days, hours, minutes
export function formatTotalHours(totalHours: number): string {
	const days = Math.floor(totalHours / 24);
	const hours = Math.floor(totalHours % 24);
	const minutes = Math.round((totalHours % 1) * 60);

	const formattedHours = String(hours).padStart(1, '0');
	const formattedMinutes = String(minutes).padStart(1, '0');

	return `${days}d : ${formattedHours}h : ${formattedMinutes}m`;
}
// Function to format a time
export function formatTime(date: Date | string | number, dateFormat: string = 'HH:mm'): string {
	return format(new Date(date), dateFormat);
}

// Function to format a date + day
export function formatDateDay(date: Date | string | number, dateFormat: string = 'dd MMM - E'): string {
	return format(new Date(date), dateFormat);
}

// Function to format a date + year + day
export function formatDateFullDay(date: Date | string | number, dateFormat: string = 'dd MMM yy - E'): string {
	return format(new Date(date), dateFormat);
}

// Function to format a date only
export function formatDate(date: Date | string | number, dateFormat: string = 'dd MMM yy'): string {
	return format(new Date(date), dateFormat);
}

// Function to format a date into "dd MMM - HH:mm"
export function formatDateTime(date: Date | string | number, dateFormat: string = 'dd MMM - HH:mm'): string {
	return format(new Date(date), dateFormat);
}

// Function to format a date into "Monday, 14th February"
export function formatDayDate(date: Date | string | number, dateFormat: string = 'EEEE, do LLLL'): string {
	return format(new Date(date), dateFormat);
}
