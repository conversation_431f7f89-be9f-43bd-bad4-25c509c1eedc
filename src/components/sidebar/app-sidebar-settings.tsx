import * as React from 'react';
import { Bolt, Building, Mail, MoveLeft, Plug, ShieldCheck, ToggleRight, Users, Workflow } from 'lucide-react';
import { Link } from 'react-router';

import { NavAccountSettings } from '@/components/sidebar/nav-account-settings';
import { Separator } from '@/components/ui/separator';
import {
	Sidebar,
	SidebarContent,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem
} from '@/components/ui/sidebar';

const data = {
	navAccount: [
		{
			title: 'General',
			url: '#',
			icon: Bolt
		},
		{
			title: 'Legal Entities',
			url: '#',
			icon: Building
		},
		{
			title: 'Users',
			url: '#',
			icon: Users
		},
		{
			title: 'Permissions',
			url: '#',
			icon: ToggleRight
		},
		{
			title: 'Security',
			url: '#',
			icon: ShieldCheck
		},
		{
			title: 'E-mail settings',
			url: '#',
			icon: Mail
		},
		{
			title: 'Workflows',
			url: '#',
			icon: Workflow,
			isActive: true,
			items: [
				{
					title: 'Approval',
					url: '#'
				}
			]
		},
		{
			title: 'Integrations',
			url: '#',
			icon: Plug,
			isActive: true,
			items: [
				{
					title: 'Veson',
					url: '#'
				},
				{
					title: 'Sedna',
					url: '#'
				}
			]
		}
	]
};

export function AppSidebarSettings({ ...props }: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar variant="inset" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton size="lg" asChild>
							<Link to="/">
								<MoveLeft />
								<span className="truncate text-sm leading-tight">Back to workspace</span>
							</Link>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<Separator />
				<NavAccountSettings items={data.navAccount} />
				<Separator />
			</SidebarContent>
		</Sidebar>
	);
}
