import { Table } from '@tanstack/react-table';
import { <PERSON><PERSON>, CircleDashed, DollarSign, ListFilter, Ship } from 'lucide-react';

import { das, statuses } from '../data/data';
import { Button } from '@/components/ui/button';
import { DataTableCompactFilter } from '@/components/data-table/data-table-compact-filter';
import { DataTableSearch } from '@/components/data-table/data-table-search';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { DataTableFilterCommand } from '@/components/data-table/data-table-filter-command';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

function getVesselValues<TData>(table: Table<TData>) {
	const vesselNameColumn = table.getColumn('vesselName');
	const columnValues = vesselNameColumn
		? Array.from(new Set((vesselNameColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getPortValues<TData>(table: Table<TData>) {
	const portNameColumn = table.getColumn('portName');
	const columnValues = portNameColumn
		? Array.from(new Set((portNameColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getDaValues<TData>(table: Table<TData>) {
	const daColumn = table.getColumn('da');
	const columnValues = daColumn
		? Array.from(new Set((daColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => {
		const da = das.find(da => da.value === value);
		return {
			label: da?.label ?? value,
			value: da?.value ?? value,
			icon: undefined
		};
	});
}

function getStatusValues<TData>(table: Table<TData>) {
	const statusColumn = table.getColumn('status');
	const columnValues = statusColumn
		? Array.from(new Set((statusColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => {
		const status = statuses.find(da => da.value === value);
		return {
			label: status?.label ?? value,
			value: status?.value ?? value,
			icon: status?.icon
		};
	});
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;

	const dropdownMenuFilter = (
		<DropdownMenuContent align="start" className="min-w-48">
			<DropdownMenuLabel>Filters</DropdownMenuLabel>
			<DropdownMenuSeparator />
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Ship className="mr-1 size-3.5" />
					Vessel
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('vesselName')}
							title="Vessel"
							options={getVesselValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Anchor className="mr-1 size-3.5" />
					Port
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('portName')}
							title="Port"
							options={getPortValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<DollarSign className="mr-1 size-3.5" />
					DA Type
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('da')}
							title="DA Type"
							options={getDaValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleDashed className="mr-1 size-3.5" />
					DA Status
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('status')}
							title="DA Status"
							options={getStatusValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
		</DropdownMenuContent>
	);

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center">
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						{isFiltered ? (
							<Button variant="ghost" size="xs" className="w-0 px-0"></Button>
						) : (
							<Button variant="ghost" size="xs">
								<ListFilter />
								Filter
							</Button>
						)}
					</DropdownMenuTrigger>
					{dropdownMenuFilter}
				</DropdownMenu>
				{table.getColumn('vesselName') && (
					<DataTableCompactFilter
						column={table.getColumn('vesselName')}
						title="Vessel"
						options={getVesselValues(table)}
						icon={Ship}
					/>
				)}
				{table.getColumn('portName') && (
					<DataTableCompactFilter
						column={table.getColumn('portName')}
						title="Port"
						options={getPortValues(table)}
						icon={Anchor}
					/>
				)}
				{table.getColumn('da') && (
					<DataTableCompactFilter
						column={table.getColumn('da')}
						title="DA Type"
						options={getDaValues(table)}
						icon={DollarSign}
					/>
				)}
				{table.getColumn('status') && (
					<DataTableCompactFilter
						column={table.getColumn('status')}
						title="DA Status"
						options={getStatusValues(table)}
						icon={CircleDashed}
					/>
				)}
				{isFiltered && (
					<TooltipProvider>
						<DropdownMenu>
							<Tooltip>
								<TooltipTrigger asChild>
									<DropdownMenuTrigger asChild>
										<Button variant="ghost" size="icon" className="h-7 w-7">
											<ListFilter />
										</Button>
									</DropdownMenuTrigger>
								</TooltipTrigger>
								<TooltipContent>Filter</TooltipContent>
							</Tooltip>
							{dropdownMenuFilter}
						</DropdownMenu>
					</TooltipProvider>
				)}
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
