import { ColumnDef } from '@tanstack/react-table';
import { AlertTriangle, Diamond, UserCog } from 'lucide-react';
import { das, statuses } from '../../data/data';
import { disbursement } from '../../data/schema-old';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { formatAmount, formatDateTime } from '@/common/utils/formatUtils';

export const columns: ColumnDef<disbursement>[] = [
	{
		accessorKey: 'groupId',
		meta: {
			label: 'Voyage ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Voyage ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('groupId')}</div>
	},
	{
		accessorKey: 'vesselName',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => <div className="font-medium">{row.getValue('vesselName')}</div>
	},
	{
		accessorKey: 'portName',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" className="min-w-[120px]" />,
		cell: ({ row }) => {
			const port = row.original;

			if (!port) {
				return null;
			}

			return (
				<span>
					{port.portName}, {port.portCountryCode}
				</span>
			);
		}
	},
	{
		accessorKey: 'etdAtd',
		meta: {
			label: 'ETD/ATD',
			className: 'border-r'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETD/ATD" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etdAtd'))}</div>
	},
	{
		accessorKey: 'agentName',
		meta: {
			label: 'Lead agent'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Lead agent" className="min-w-[120px]" />,
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('agentName')}>
				{row.getValue('agentName')}
			</div>
		)
	},
	{
		accessorKey: 'daId',
		meta: {
			label: 'DA ID',
			className: 'bg-muted/20 border-l border-r'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DA ID" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('daId')}</div>
	},
	{
		accessorKey: 'stage',
		meta: {
			label: 'DA Stage'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DA Stage" />,
		cell: ({ row }) => {
			const da = das.find(da => da.value === row.getValue('da'));
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status || !da) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					<Diamond className={cn('size-3', status.color)} />
					<span className="font-medium">{da.label}</span>
					<span className="text-muted-foreground">{status.label}</span>
				</Badge>
			);
		}
	},
	{
		accessorKey: 'pdaAmount',
		meta: {
			label: 'PDA',
			className: 'border-r border-l bg-muted/20'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="PDA" reverse className="min-w-[100px]" />,
		cell: ({ row }) => {
			const amount = row.original;
			const pdaAmount = parseFloat(row.getValue('pdaAmount'));

			if (!pdaAmount || pdaAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(pdaAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(pdaAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'ddaAmount',
		meta: {
			label: 'DDA',
			className: 'border-r'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DDA" reverse className="min-w-[100px]" />,
		cell: ({ row }) => {
			const amount = row.original;
			const ddaAmount = parseFloat(row.getValue('ddaAmount'));

			if (!ddaAmount || ddaAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(ddaAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(ddaAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'fdaAmount',
		meta: {
			label: 'FDA',
			className: 'border-r bg-muted/20'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="FDA" reverse className="min-w-[100px]" />,
		cell: ({ row }) => {
			const amount = row.original;
			const fdaAmount = parseFloat(row.getValue('fdaAmount'));

			if (!fdaAmount || fdaAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(fdaAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(fdaAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'paymentsAmount',
		meta: {
			label: 'My Payments',
			className: 'border-r'
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="My Payments" reverse className="min-w-[100px]" />
		),
		cell: ({ row }) => {
			const amount = row.original;
			const paymentsAmount = parseFloat(row.getValue('paymentsAmount'));

			if (!paymentsAmount || paymentsAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(paymentsAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(paymentsAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'refundAmount',
		meta: {
			label: 'Refund',
			className: 'border-r',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Refund" reverse className="min-w-[100px]" />
		),
		cell: ({ row }) => {
			const amount = row.original;
			const refundAmount = parseFloat(row.getValue('refundAmount'));

			if (!refundAmount || refundAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(refundAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(refundAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'balanceAmount',
		meta: {
			label: 'Balance',
			className: 'border-r bg-muted/20'
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Balance" reverse className="min-w-[100px]" />
		),
		cell: ({ row }) => {
			const amount = row.original;
			const balanceAmount = parseFloat(row.getValue('balanceAmount'));

			if (!balanceAmount || balanceAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(balanceAmount)}</div>;
			}

			if (balanceAmount < 0) {
				return (
					<div className="text-destructive text-right font-medium">
						<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
						{formatAmount(balanceAmount)}
					</div>
				);
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(balanceAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'issues',
		meta: {
			label: 'Issues',
			className: 'min-w-[120px]'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Issues" />,
		cell: ({ row }) => {
			const issues = parseFloat(row.getValue('issues'));

			if (!issues) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					<AlertTriangle className="text-destructive size-3" />
					<span>{issues}</span>
					<span className="text-muted-foreground">{issues === 1 ? 'issue' : 'issues'}</span>
				</Badge>
			);
		}
	},
	{
		accessorKey: 'pendingWith',
		meta: {
			label: 'Pending with'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Pending with" />,
		cell: ({ row }) => (
			<Badge variant="secondary" className="rounded-full">
				<UserCog className="text-primary size-3" />
				<span className="capitalize">{row.getValue('pendingWith')}</span>
			</Badge>
		)
	},
	{
		accessorKey: 'etaAta',
		meta: {
			label: 'ETA/ATA',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA/ATA" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etaAta'))}</div>
	},
	{
		accessorKey: 'etb',
		meta: {
			label: 'ETB',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETB" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etb'))}</div>
	},
	{
		accessorKey: 'portCallId',
		meta: {
			label: 'Port Call ID',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Port Call ID" className="min-w-[100px]" />
		),
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('portCallId')}</div>
	},
	{
		accessorKey: 'appointingParty',
		meta: {
			label: 'Appointing Party',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Appointing Party" className="min-w-[120px]" />
		),
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('appointingParty')}>
				{row.getValue('appointingParty')}
			</div>
		)
	},
	{
		accessorKey: 'nominatingParty',
		meta: {
			label: 'Nominating Party',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Nominating Party" className="min-w-[120px]" />
		),
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('nominatingParty')}>
				{row.getValue('nominatingParty')}
			</div>
		)
	},
	{
		accessorKey: 'da',
		meta: {
			label: 'DA Type',
			className: 'text-center',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DA Type" middle />,
		cell: ({ row }) => {
			const da = das.find(da => da.value === row.getValue('da'));

			if (!da) {
				return null;
			}

			return (
				<Badge variant="outline" className={cn('rounded-full', da.color)}>
					<span>{da.label}</span>
				</Badge>
			);
		}
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					{status.icon && <status.icon className={cn('h-3 w-3', status.color)} />}
					<span>{status.label}</span>
				</Badge>
			);
		}
	}
];
