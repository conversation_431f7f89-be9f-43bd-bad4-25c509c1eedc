import { ColumnDef } from '@tanstack/react-table';
import { Bookmark } from 'lucide-react';
import { das, statuses } from '../../data/data';
import { disbursement } from '../../data/schema-old';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { formatAmount, formatDateTime } from '@/common/utils/formatUtils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export const columns: ColumnDef<disbursement>[] = [
	{
		accessorKey: 'groupId',
		meta: {
			label: 'Voyage ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Voyage ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('groupId')}</div>
	},
	{
		accessorKey: 'portCallId',
		meta: {
			label: 'Port Call ID',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Port Call ID" className="min-w-[100px]" />
		),
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('portCallId')}</div>
	},
	{
		accessorKey: 'vesselName',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => <div className="font-medium">{row.getValue('vesselName')}</div>
	},
	{
		accessorKey: 'portName',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" className="min-w-[120px]" />,
		cell: ({ row }) => {
			const port = row.original;

			if (!port) {
				return null;
			}

			return (
				<span>
					{port.portName}, {port.portCountryCode}
				</span>
			);
		}
	},
	{
		accessorKey: 'eta',
		meta: {
			label: 'ETA',
			className: 'border-r border-l bg-muted/20',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('eta'))}</div>
	},
	{
		accessorKey: 'etb',
		meta: {
			label: 'ETB',
			className: 'border-r',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETB" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etb'))}</div>
	},
	{
		accessorKey: 'ata',
		meta: {
			label: 'ATA',
			className: 'border-r bg-muted/20',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ATA" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('ata'))}</div>
	},
	{
		accessorKey: 'atd',
		meta: {
			label: 'ATD',
			className: 'border-r',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ATD" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('atd'))}</div>
	},
	{
		accessorKey: 'etaAta',
		meta: {
			label: 'ETA/ATA',
			className: 'border-r bg-muted/20',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA/ATA" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etaAta'))}</div>
	},
	{
		accessorKey: 'etdAtd',
		meta: {
			label: 'ETD/ATD',
			className: 'border-r',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETD/ATD" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('etdAtd'))}</div>
	},
	{
		accessorKey: 'agentName',
		meta: {
			label: 'Lead agent'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Lead agent" className="min-w-[120px]" />,
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('agentName')}>
				{row.getValue('agentName')}
			</div>
		)
	},
	{
		accessorKey: 'daId',
		meta: {
			label: 'DA ID',
			className: 'bg-muted/20 border-l border-r'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DA ID" className="min-w-[100px]" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('daId')}</div>
	},
	{
		accessorKey: 'da',
		meta: {
			label: 'DA Type',
			className: 'text-center'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="DA Type" middle />,
		cell: ({ row }) => {
			const da = das.find(da => da.value === row.getValue('da'));

			if (!da) {
				return null;
			}

			return (
				<Badge variant="outline" className={cn('rounded-full', da.color)}>
					<span>{da.label}</span>
				</Badge>
			);
		}
	},
	{
		accessorKey: 'daAmount',
		meta: {
			label: 'Amount',
			className: 'border-r border-l bg-muted/20'
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Amount" reverse className="min-w-[100px]" />
		),
		cell: ({ row }) => {
			const amount = row.original;
			const daAmount = parseFloat(row.getValue('daAmount'));

			if (!daAmount || daAmount === 0) {
				return <div className="text-muted-foreground text-right">{formatAmount(daAmount)}</div>;
			}

			return (
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs uppercase">{amount.currency}</span>{' '}
					{formatAmount(daAmount)}
				</div>
			);
		}
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status',
			className: 'min-w-[120px] w-full'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					{status.icon && <status.icon className={cn('h-3 w-3', status.color)} />}
					<span>{status.label}</span>
				</Badge>
			);
		}
	},
	{
		accessorKey: 'approvedBy',
		meta: {
			label: 'Approved by',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Approved by" />,
		cell: ({ row }) => (
			<div className="flex items-center gap-2">
				<Avatar className="h-5 w-5 rounded-sm">
					<AvatarImage src="" alt="" />
					<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
						JS
					</AvatarFallback>
				</Avatar>
				<span className="text-muted-foreground max-w-[120px] truncate text-sm">
					{row.getValue('approvedBy')}
				</span>
			</div>
		)
	},
	{
		accessorKey: 'subAgent',
		meta: {
			label: 'Sub Agent',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Sub Agent" className="min-w-[120px]" />,
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('subAgent')}>
				{row.getValue('subAgent')}
			</div>
		)
	},
	{
		accessorKey: 'labels',
		meta: {
			label: 'Labels',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Labels" />,
		cell: ({ row }) => {
			const labels = row.getValue('labels');

			if (!labels) {
				return null;
			}

			return (
				<div className="flex items-center gap-1">
					{(labels as Array<{ label: string }>).map((label: { label: string }) => (
						<Badge key={label.label} variant="secondary" className="rounded-full">
							<Bookmark className="size-3 text-sky-500" />
							{label.label}
						</Badge>
					))}
				</div>
			);
		}
	},
	{
		accessorKey: 'appointingParty',
		meta: {
			label: 'Appointing Party',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Appointing Party" className="min-w-[120px]" />
		),
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('appointingParty')}>
				{row.getValue('appointingParty')}
			</div>
		)
	},
	{
		accessorKey: 'nominatingParty',
		meta: {
			label: 'Nominating Party',
			defaultHidden: true
		},
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Nominating Party" className="min-w-[120px]" />
		),
		cell: ({ row }) => (
			<div className="max-w-[160px] truncate" title={row.getValue('nominatingParty')}>
				{row.getValue('nominatingParty')}
			</div>
		)
	}
];
