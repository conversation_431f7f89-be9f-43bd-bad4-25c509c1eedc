import { BriefcaseBusiness, Diamond, DollarSign, Files, Hash, Landmark, ShieldCheck, Text } from 'lucide-react';
import { useNavigate } from 'react-router';
import { cn } from '@/lib/utils';
import { statuses as globalStatuses } from '@/constants/statuses';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHeader, TableRow, TableHead } from '@/components/ui/table';

const disbursements1 = [
	{
		id: '1',
		name: 'Final DA',
		type: 'fda',
		status: 'pending_approval',
		amount: '26,522.00',
		payment: '10,000.00',
		currency: 'USD',
		updated: '3 hrs ago'
	},
	{
		id: '2',
		name: 'Supplementary DA ',
		type: 'sda',
		status: 'approved',
		amount: '12,345.00',
		payment: '8,000.00',
		currency: 'USD',
		updated: '1 day ago'
	},
	{
		id: '3',
		name: 'Proforma DA',
		type: 'pda',
		status: 'approved',
		amount: '12,345.00',
		payment: '12,345.00',
		currency: 'USD',
		updated: '1 day ago'
	}
];

const daTypes = [
	{
		type: 'pda',
		label: 'PDA',
		color: 'border-amber-500'
	},
	{
		type: 'dda',
		label: 'DDA',
		color: 'border-indigo-500'
	},
	{
		type: 'fda',
		label: 'FDA',
		color: 'border-emerald-500'
	},
	{
		type: 'sda',
		label: 'SDA',
		color: 'border-blue-500'
	}
];

const DaTypeBadge = ({ type }: { type: string }) => {
	const daType = daTypes.find(da => da.type === type);

	if (!daType) return <span>{type}</span>;

	return (
		<Badge variant="outline" className={cn('rounded-full', daType.color)}>
			<span>{daType.label}</span>
		</Badge>
	);
};

const StatusBadge = ({ status }: { status: string }) => {
	const statusConfig = globalStatuses.find(s => s.value === status);

	if (!statusConfig) return <span>{status}</span>;

	const { icon: Icon, color, label } = statusConfig;

	return (
		<Badge variant="outline" className="rounded-full">
			{Icon && <Icon className={cn('h-3 w-3', color)} />}
			<span className="text-muted-foreground">{label}</span>
		</Badge>
	);
};

export default function PortCallFinancial() {
	const navigate = useNavigate();

	return (
		<div className="grid gap-6">
			<div className="flex items-center pt-4">
				<h3 className="flex-1 text-base font-semibold">Magellan Chartering S.A.</h3>
				<Button variant="ghost" size="xs" className="text-muted-foreground">
					<Files />
					Invoices
					<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
						9
					</Badge>
				</Button>
			</div>
			<div className="flex w-full flex-col items-center gap-6 lg:flex-row">
				<div className="w-full flex-1">
					<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
						<div className="text-muted-foreground flex items-center gap-2">
							<Hash className="size-4" />
							Client ID
						</div>
						<div>MC-2935-1</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Text className="size-4" />
							Customer ref.
						</div>
						<div>REF-123</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<BriefcaseBusiness className="size-4" />
							Care of (C/o)
						</div>
						<div>Magellan Shipping</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<DollarSign className="size-4" />
							Pre-funding
						</div>
						<div className="text-muted-foreground">---</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Landmark className="size-4" />
							Beneficiary
						</div>
						<div>Cargill Navigation</div>
					</div>
				</div>
				<div className="w-full flex-1">
					<div className="bg-card rounded-lg border p-4">
						<div className="grid grid-cols-[160px_minmax(0px,1fr)] items-center gap-y-3 text-sm">
							<div className="text-muted-foreground">Total costs</div>
							<div className="text-right">
								<span className="text-muted-foreground text-xs">USD</span>
								&nbsp;
								<span className="text-md font-semibold">26,522.00</span>
							</div>
							<div className="text-muted-foreground">Payments (0%)</div>
							<div className="text-right">
								<span className="text-muted-foreground text-xs">USD</span>
								&nbsp;
								<span className="text-md font-semibold">00.00</span>
							</div>
							<div className="text-muted-foreground">Refund</div>
							<div className="text-right">
								<span className="text-muted-foreground text-xs">USD</span>
								&nbsp;
								<span className="text-md font-semibold">00.00</span>
							</div>
							<div className="col-span-2">
								<Separator />
							</div>
							<div className="font-medium">Balance</div>
							<div className="text-right">
								<span className="text-muted-foreground text-sm">USD</span>
								&nbsp;
								<span className="text-lg font-semibold">26,522.00</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<Separator />
			<div className="grid gap-4">
				<div className="flex items-center gap-2">
					<h4 className="text-md font-semibold">DISB-0035-MNT</h4>
					<Separator className="w-6" />
					<Badge variant="outline" className="rounded-full">
						<Diamond className="size-3 text-amber-500" />
						<span className="font-medium">FDA</span>
						<span className="text-muted-foreground">Pending approval</span>
					</Badge>
				</div>
				<div className="bg-card rounded-lg border py-1">
					<Table>
						<TableHeader className="text-xs hover:bg-transparent">
							<TableRow className="hover:bg-transparent">
								<TableHead className="pl-4 font-normal">Disbursement</TableHead>
								<TableHead className="font-normal">Status</TableHead>
								<TableHead className="text-right font-normal">DA Costs</TableHead>
								<TableHead className="text-right font-normal">Payments</TableHead>
								<TableHead className="pr-4 text-right font-normal">Updated</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{disbursements1.map(item => (
								<TableRow
									key={item.id}
									onClick={() => navigate(`/operations/port-calls/ABX-123A/disbursement`)}
								>
									<TableCell className="pl-4">
										<div className="flex items-center gap-2">
											<DaTypeBadge type={item.type} />
											<span className="font-medium">{item.name}</span>
										</div>
									</TableCell>
									<TableCell>
										<StatusBadge status={item.status} />
									</TableCell>
									<TableCell className="text-right">
										<div className="flex items-center justify-end gap-2">
											<div className="flex items-end gap-1">
												<span className="text-muted-foreground text-xs">{item.currency}</span>
												<span className="font-medium">{item.amount}</span>
											</div>
										</div>
									</TableCell>
									<TableCell className="text-right">
										<div className="flex items-center justify-end gap-2">
											<div className="flex items-end gap-1">
												<span className="text-muted-foreground text-xs">{item.currency}</span>
												<span className="font-medium">{item.payment}</span>
											</div>
										</div>
									</TableCell>
									<TableCell className="pr-4 text-right">
										<span className="text-muted-foreground">{item.updated}</span>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>
			<Separator />
			<div className="grid gap-4">
				<h3 className="w-full text-base font-semibold">Payment details</h3>
				<div className="grid w-full gap-4 rounded-lg border p-4 text-sm">
					<div className="flex items-center gap-2">
						<div className="text-md font-medium">United Overseas Bank</div>
						<ShieldCheck className="size-4 text-emerald-500" />
					</div>
					<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-3 text-sm sm:grid-cols-[auto_minmax(0px,_1fr)_auto_minmax(0px,_1fr)]">
						<div className="text-muted-foreground">IBAN</div>
						<div>SDJSDFT9384RU534H</div>
						<div className="text-muted-foreground">Account No</div>
						<div>ACC-123-456-789-0</div>
						<div className="text-muted-foreground">Swift</div>
						<div>SWIFT-123-12212</div>
						<div className="text-muted-foreground">Beneficiary</div>
						<div>Raffles Place Branch</div>
					</div>
				</div>
			</div>
		</div>
	);
}
