export default function PortCallGeneral() {
	return (
		<div className="grid grid-cols-2 gap-6">
			<div className="bg-card grid gap-4 rounded-xl border p-6">
				<div className="text-muted-foreground w-full text-right text-xs font-normal">Balance</div>
				<div className="flex w-full items-end justify-end gap-2">
					<span className="text-muted-foreground text-sm">USD</span>
					<span className="text-2xl leading-7 font-semibold">183,987.00</span>
				</div>
			</div>
			<div className="bg-card grid gap-4 rounded-xl border p-6">
				<div className="text-muted-foreground w-full text-right text-xs font-normal">Demurrage</div>
				<div className="flex w-full items-end justify-end gap-2">
					<span className="text-muted-foreground text-sm">USD</span>
					<span className="text-2xl leading-7 font-semibold">23,344.00</span>
				</div>
			</div>
			<div className="bg-card col-span-2 grid gap-4 rounded-xl border p-6 text-center">
				<div className="flex items-start">
					<div className="flex flex-1 flex-col items-start gap-1">
						<div className="flex items-center gap-2">
							<span className="text-base font-semibold">Shell Terminal 13</span>{' '}
							<span className="text-muted-foreground">•</span>{' '}
							<span className="text-muted-foreground">NLRTM</span>
						</div>
						<div className="text-sm">
							<span className="font-semibold">3,000.000</span> mts - Iron Ore{' '}
							<span className="text-muted-foreground">(Loading 72%)</span>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<div>
							<span className="font-semibold">35</span>{' '}
							<span className="text-muted-foreground text-sm">hrs</span>
						</div>
						<div>
							<span className="font-semibold">12</span>{' '}
							<span className="text-muted-foreground text-sm">hrs</span>
						</div>
					</div>
				</div>
				<img src="/static/media/img-timeline.svg" className="w-full" alt="" />
				<div className="-mt-4 flex items-center justify-between text-sm">
					<div>Sat, 25 May - 08:02</div>
					<div>Tue, 28 May - 10:00</div>
				</div>
			</div>
		</div>
	);
}
