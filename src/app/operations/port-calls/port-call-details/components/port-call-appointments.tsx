import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Edit3 } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function PortCallAppointments() {
	const navigate = useNavigate();

	return (
		<div className="grid gap-4">
			<h3 className="text-base font-semibold">Appointments</h3>
			<Table>
				<TableBody>
					<TableRow onClick={() => navigate(`/operations/appointments/new-ai`)}>
						<TableCell className="w-1/2">
							<div className="flex items-center gap-2">
								<Edit3 className="text-muted-foreground h-4 w-4" />
								<span className="font-medium">Noordriver Shipping B.V.</span>
							</div>
						</TableCell>
						<TableCell>
							<Badge variant="outline" className="rounded-full">
								<Edit3 className="text-muted-foreground size-3" />
								<span className="text-muted-foreground">Draft</span>
							</Badge>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-2">
								<div className="text-muted-foreground text-sm">08 Jun 24 - 10:53</div>
								<Avatar className="size-5 rounded-sm">
									<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
										JD
									</AvatarFallback>
								</Avatar>
							</div>
						</TableCell>
					</TableRow>
					<TableRow onClick={() => navigate(`/operations/appointments/abx-123a/preview`)}>
						<TableCell className="w-1/2">
							<div className="flex items-center gap-2">
								<CheckCheck className="text-muted-foreground h-4 w-4" />
								<span className="font-medium">LKL Oceantrade B.V.</span>
							</div>
						</TableCell>
						<TableCell>
							<Badge variant="outline" className="rounded-full">
								<Clock className="size-3 text-amber-500" />
								<span className="text-muted-foreground">Pending</span>
							</Badge>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-2">
								<div className="text-muted-foreground text-sm">08 Jun 24 - 10:53</div>
								<Avatar className="size-5 rounded-sm">
									<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
									<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
										JD
									</AvatarFallback>
								</Avatar>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
