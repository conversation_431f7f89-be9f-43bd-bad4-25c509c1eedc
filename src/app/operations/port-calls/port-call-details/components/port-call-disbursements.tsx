import { useNavigate } from 'react-router';
import { cn } from '@/lib/utils';
import { statuses as globalStatuses } from '@/constants/statuses';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

const disbursements = [
	{
		id: '1',
		name: 'Final DA',
		type: 'fda',
		status: 'pending_approval',
		amount: '26,522.00',
		currency: 'USD'
	},
	{
		id: '2',
		name: 'Supplementary DA ',
		type: 'sda',
		status: 'approved',
		amount: '12,345.00',
		currency: 'USD'
	},
	{
		id: '3',
		name: 'Proforma DA',
		type: 'pda',
		status: 'approved',
		amount: '12,345.00',
		currency: 'USD'
	}
];

const daTypes = [
	{
		type: 'pda',
		label: 'PDA',
		color: 'border-amber-500'
	},
	{
		type: 'dda',
		label: 'DDA',
		color: 'border-indigo-500'
	},
	{
		type: 'fda',
		label: 'FDA',
		color: 'border-emerald-500'
	},
	{
		type: 'sda',
		label: 'SDA',
		color: 'border-blue-500'
	}
];

const DaTypeBadge = ({ type }: { type: string }) => {
	const daType = daTypes.find(da => da.type === type);

	if (!daType) return <span>{type}</span>;

	return (
		<Badge variant="outline" className={cn('rounded-full', daType.color)}>
			<span>{daType.label}</span>
		</Badge>
	);
};

const StatusBadge = ({ status }: { status: string }) => {
	const statusConfig = globalStatuses.find(s => s.value === status);

	if (!statusConfig) return <span>{status}</span>;

	const { icon: Icon, color, label } = statusConfig;

	return (
		<Badge variant="outline" className="rounded-full">
			{Icon && <Icon className={cn('h-3 w-3', color)} />}
			<span className="text-muted-foreground">{label}</span>
		</Badge>
	);
};

export default function PortCallDisbursements() {
	const navigate = useNavigate();

	return (
		<div className="grid gap-4">
			<h3 className="text-base font-semibold">
				Disbursements <span className="text-muted-foreground font-normal">(3)</span>
			</h3>
			<Table>
				<TableBody>
					{disbursements.map(item => (
						<TableRow
							key={item.id}
							onClick={() => navigate(`/operations/port-calls/ABX-123A/disbursement`)}
						>
							<TableCell className="w-1/2">
								<div className="flex items-center gap-2">
									<DaTypeBadge type={item.type} />
									<span className="font-medium">{item.name}</span>
									<span className="text-muted-foreground text-lg">•</span>
									<span className="text-muted-foreground">DA Stage</span>
								</div>
							</TableCell>
							<TableCell>
								<StatusBadge status={item.status} />
							</TableCell>
							<TableCell className="text-right">
								<div className="flex items-center justify-end gap-2">
									<div className="flex items-end gap-1">
										<span className="text-muted-foreground text-xs">{item.currency}</span>
										<span className="font-medium">{item.amount}</span>
									</div>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
}
