import {
	<PERSON><PERSON>,
	<PERSON><PERSON>igLeftDash,
	ArrowBigRightDash,
	Box,
	Calendar,
	Check<PERSON>heck,
	ChevronRight,
	Clock,
	Droplets,
	Layers2,
	Warehouse
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

const bunkers = [
	{
		name: 'IFO',
		arrival: '12,32',
		supplied: '14,00',
		departure: '11,00'
	},
	{
		name: 'VLSFO',
		arrival: '12,32',
		supplied: '14,00',
		departure: '11,00'
	},
	{
		name: 'MGO',
		arrival: '12,32',
		supplied: '14,00',
		departure: '11,00'
	}
];

export default function PortCallOps() {
	return (
		<div className="grid gap-6">
			<div className="pt-4">
				<Tabs defaultValue="itinerary" className="w-full">
					<div className="flex items-center justify-between gap-2">
						<h3 className="text-base font-semibold">Itinerary</h3>
						<TabsList className="flex h-auto border bg-transparent p-0">
							<TabsTrigger
								value="itinerary"
								className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
							>
								<div className="flex items-center gap-2">
									<Anchor className="size-3.5" />
									Rotterdam, NL
								</div>
							</TabsTrigger>
							<TabsTrigger
								value="terminal1"
								className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
							>
								<div className="flex items-center gap-2">
									<Warehouse className="size-3.5" />
									Shell Terminal 13 (L)
								</div>
							</TabsTrigger>
							<TabsTrigger
								value="terminal2"
								className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
							>
								<div className="flex items-center gap-2">
									<Warehouse className="size-3.5" />
									Terminal 2 (D)
								</div>
							</TabsTrigger>
						</TabsList>
					</div>
					<TabsContent value="itinerary" className="flex flex-1 flex-col gap-6">
						<div className="flex w-full flex-col items-center gap-6 pt-4 lg:flex-row">
							<div className="w-full flex-1">
								<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
									<div className="text-muted-foreground flex items-center gap-2">
										<Anchor className="size-4" />
										Current port
									</div>
									<div>Rotterdam, NL</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										Arrival pilot station
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										ETA
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										ETD
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<ArrowBigRightDash className="size-4" />
										Next port
									</div>
									<div>Singapore, SG</div>
								</div>
							</div>
							<div className="w-full flex-1">
								<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
									<div className="text-muted-foreground flex items-center gap-2">
										<ArrowBigLeftDash className="size-4" />
										Previous port
									</div>
									<div>Antwerp, BE</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										Departure pilot station
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										ATA
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										ATD
									</div>
									<div>16 May - 10:43</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-4" />
										Next port ETA
									</div>
									<div>16 May - 10:43</div>
								</div>
							</div>
						</div>
						<div className="grid gap-2">
							<h4 className="text-md font-semibold">Itinerary remarks</h4>
							<p className="text-muted-foreground text-sm italic">
								Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum
								has been the industry&apos;s standard dummy text ever since the 1500s, when an unknown
								printer took a galley of type and scrambled it to make a type specimen book.
							</p>
						</div>
						<div className="grid gap-2">
							<h4 className="text-md font-semibold">Cargoes</h4>
							<Button
								variant="outline"
								className="group h-auto justify-start gap-3 rounded-lg bg-transparent p-3"
							>
								<Box className="text-primary size-4" />
								<div className="flex flex-1 items-center gap-2">
									<div>
										<span className="text-md font-medium">2,500.000</span> mts
									</div>
									<Separator className="w-4" />
									<span className="text-md font-medium">Iron Ore</span>
									<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
										L
									</Badge>
								</div>
								<div className="text-muted-foreground flex-1 font-normal">Coal Trading B.V.</div>
								<div className="flex flex-1 items-center gap-2">
									<Progress value={78} />
									<span className="text-muted-foreground text-xs font-normal">78%</span>
								</div>
								<ChevronRight className="text-muted-foreground opacity-0 group-hover:opacity-100" />
							</Button>
						</div>
					</TabsContent>
				</Tabs>
			</div>
			<Separator />
			<div className="grid gap-4">
				<h3 className="text-base font-semibold">Drafts</h3>
				<div className="flex w-full flex-col items-center gap-6 lg:flex-row">
					<div className="flex flex-1 flex-col gap-4">
						<div className="text-muted-foreground text-xs font-medium uppercase">On arrival</div>
						<div className="flex items-center gap-4 text-sm">
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Fore</div>
								<div>
									<span className="font-semibold">11,00</span> m
								</div>
							</div>
							<Separator orientation="vertical" className="h-6" />
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Middle</div>
								<div>
									<span className="font-semibold">12,00</span> m
								</div>
							</div>
							<Separator orientation="vertical" className="h-6" />
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Aft</div>
								<div>
									<span className="font-semibold">12,00</span> m
								</div>
							</div>
						</div>
					</div>
					<div className="flex flex-1 flex-col gap-4">
						<div className="text-muted-foreground text-xs font-medium uppercase">On departure</div>
						<div className="flex items-center gap-4 text-sm">
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Fore</div>
								<div>
									<span className="font-semibold">11,00</span> m
								</div>
							</div>
							<Separator orientation="vertical" className="h-6" />
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Middle</div>
								<div>
									<span className="font-semibold">12,00</span> m
								</div>
							</div>
							<Separator orientation="vertical" className="h-6" />
							<div className="flex-1">
								<div className="text-muted-foreground text-xs">Aft</div>
								<div>
									<span className="font-semibold">12,00</span> m
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<Separator />
			<div className="grid gap-4">
				<h3 className="text-base font-semibold">Bunkers on board</h3>
				<Table>
					<TableHeader className="text-xs hover:bg-transparent">
						<TableRow>
							<TableHead className="font-normal">Bunker</TableHead>
							<TableHead className="font-normal">On arrival</TableHead>
							<TableHead className="font-normal">Supplied</TableHead>
							<TableHead className="font-normal">On departure</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{bunkers.map(bunker => (
							<TableRow key={bunker.name}>
								<TableCell>
									<div className="flex items-center gap-2">
										<Droplets className="text-muted-foreground size-4" />
										<div className="font-semibold">{bunker.name}</div>
									</div>
								</TableCell>
								<TableCell>
									<span className="font-semibold">{bunker.arrival}</span> mts
								</TableCell>
								<TableCell>
									<span className="font-semibold">{bunker.supplied}</span> mts
								</TableCell>
								<TableCell>
									<span className="font-semibold">{bunker.departure}</span> mts
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
			<Separator />
			<div className="grid gap-4">
				<h3 className="text-base font-semibold">Port services</h3>
				<Table>
					<TableBody>
						<TableRow>
							<TableCell className="w-1/2">
								<div className="flex items-center gap-2">
									<Layers2 className="text-muted-foreground size-4" />
									<div className="font-semibold">Pilotage</div>
								</div>
							</TableCell>
							<TableCell>
								<Badge variant="outline" className="rounded-full">
									<Clock className="size-3 text-amber-500" />
									<span className="text-muted-foreground">Pending</span>
								</Badge>
							</TableCell>
							<TableCell className="text-right">
								<div className="flex items-center justify-end gap-2">
									<div className="flex items-end gap-1">
										<span className="text-muted-foreground text-xs">USD</span>
										<span className="font-medium">26,522.00</span>
									</div>
								</div>
							</TableCell>
						</TableRow>
						<TableRow>
							<TableCell className="w-1/2">
								<div className="flex items-center gap-2">
									<Layers2 className="text-muted-foreground size-4" />
									<div className="font-semibold">Towage</div>
								</div>
							</TableCell>
							<TableCell>
								<Badge variant="outline" className="rounded-full">
									<CheckCheck className="size-3 text-emerald-500" />
									<span className="text-muted-foreground">Delivered</span>
								</Badge>
							</TableCell>
							<TableCell className="text-right">
								<div className="flex items-center justify-end gap-2">
									<div className="flex items-end gap-1">
										<span className="text-muted-foreground text-xs">USD</span>
										<span className="font-medium">12,345.00</span>
									</div>
								</div>
							</TableCell>
						</TableRow>
					</TableBody>
				</Table>
			</div>
			<Separator className="bg-transparent" />
		</div>
	);
}
