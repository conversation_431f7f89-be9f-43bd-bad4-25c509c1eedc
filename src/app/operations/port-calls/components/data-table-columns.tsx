import { ColumnDef } from '@tanstack/react-table';
import { operators, portFunctions, statuses } from '../data/data';
import { portCall } from '../data/schema';
import avatarImage from '@/static/media/avatar-md.webp';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { formatDateTime } from '@/common/utils/formatUtils';

export const columns: ColumnDef<portCall>[] = [
	{
		accessorKey: 'fileId',
		meta: {
			label: 'Port Call ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port Call ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('fileId')}</div>
	},
	{
		accessorKey: 'vesselName',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => {
			const vessel = row.original;
			return <span className="font-medium">{vessel.vesselName}</span>;
		}
	},
	{
		accessorKey: 'portFunction',
		meta: {
			label: 'Function'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Function" />,
		cell: ({ row }) => {
			const portFunction = portFunctions.find(
				portFunction => portFunction.value === row.getValue('portFunction')
			);

			if (!portFunction) {
				return null;
			}

			return (
				<Badge variant="secondary" className="rounded-full">
					{portFunction.icon && <portFunction.icon className={cn('h-3 w-3', portFunction.color)} />}
					<span>{portFunction.label}</span>
				</Badge>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'port',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" />,
		cell: ({ row }) => {
			const port = row.original;
			return (
				<span>
					{port.portName}, {port.portCountryCode}
				</span>
			);
		}
	},
	{
		accessorKey: 'eta',
		meta: {
			label: 'ETA/ATA'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA/ATA" />,
		cell: ({ row }) => <div className="text-muted-foreground text-sm">{formatDateTime(row.getValue('eta'))}</div>
	},
	{
		accessorKey: 'etd',
		meta: {
			label: 'ETD/ATD'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETD/ATD" />,
		cell: ({ row }) => <div className="text-muted-foreground text-sm">{formatDateTime(row.getValue('etd'))}</div>
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					{status.icon && <status.icon className={cn('h-3 w-3', status.color)} />}
					<span>{status.label}</span>
				</Badge>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'operatorName',
		meta: {
			label: 'Operator'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Operator" />,
		cell: ({ row }) => {
			const operatorName = row.getValue('operatorName');
			const operator = operators.find(operator => operator.value === operatorName) || {
				value: operatorName,
				label: (operatorName as string)
					.split(' ')
					.map((part, index) => (index === 0 ? `${part.charAt(0)}. ` : part))
					.join(''),
				short: '-',
				avatar: avatarImage
			};

			return (
				<div className="flex items-center gap-2">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src={operator.avatar} alt={operator.label} />
						<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
							JD
						</AvatarFallback>
					</Avatar>
					<span className="text-muted-foreground max-w-[120px] truncate text-sm">{operator.label}</span>
				</div>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	}
];
