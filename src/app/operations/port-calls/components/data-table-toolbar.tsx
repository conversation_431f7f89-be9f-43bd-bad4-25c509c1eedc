import { Table } from '@tanstack/react-table';
import { <PERSON>Filter, CircleDashed, CircleUserRound } from 'lucide-react';

import { statuses } from '../data/data';
import { Button } from '@/components/ui/button';
import { DataTableCompactFilter } from '@/components/data-table/data-table-compact-filter';
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { DataTableFilterCommand } from '@/components/data-table/data-table-filter-command';
import { DataTableSearch } from '@/components/data-table/data-table-search';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

function getStatusValues<TData>(table: Table<TData>) {
	const statusColumn = table.getColumn('status');
	const columnValues = statusColumn
		? Array.from(new Set((statusColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => {
		const status = statuses.find(status => status.value === value);
		return {
			label: status?.label ?? value,
			value: status?.value ?? value,
			icon: status?.icon
		};
	});
}

function getOperatorValues<TData>(table: Table<TData>) {
	const operatorColumn = table.getColumn('operatorName');
	const columnValues = operatorColumn
		? Array.from(new Set((operatorColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;

	const dropdownMenuFilter = (
		<DropdownMenuContent align="start" className="min-w-48">
			<DropdownMenuLabel>Filters</DropdownMenuLabel>
			<DropdownMenuSeparator />
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleDashed className="mr-1 size-3.5" />
					Status
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('status')}
							title="Status"
							options={getStatusValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleUserRound className="mr-1 size-3.5" />
					Operator
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('operatorName')}
							title="Operator"
							options={getOperatorValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
		</DropdownMenuContent>
	);

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center">
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						{isFiltered ? (
							<Button variant="ghost" size="xs" className="w-0 px-0"></Button>
						) : (
							<Button variant="ghost" size="xs">
								<ListFilter />
								Filter
							</Button>
						)}
					</DropdownMenuTrigger>
					{dropdownMenuFilter}
				</DropdownMenu>

				{table.getColumn('status') && (
					<DataTableCompactFilter
						column={table.getColumn('status')}
						title="Status"
						options={getStatusValues(table)}
						icon={CircleDashed}
					/>
				)}
				{table.getColumn('operatorName') && (
					<DataTableCompactFilter
						column={table.getColumn('operatorName')}
						title="Operator"
						options={getOperatorValues(table)}
						icon={CircleUserRound}
					/>
				)}

				{isFiltered && (
					<TooltipProvider>
						<DropdownMenu>
							<Tooltip>
								<TooltipTrigger asChild>
									<DropdownMenuTrigger asChild>
										<Button variant="ghost" size="icon" className="h-7 w-7">
											<ListFilter />
										</Button>
									</DropdownMenuTrigger>
								</TooltipTrigger>
								<TooltipContent>Filter</TooltipContent>
							</Tooltip>
							{dropdownMenuFilter}
						</DropdownMenu>
					</TooltipProvider>
				)}
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
