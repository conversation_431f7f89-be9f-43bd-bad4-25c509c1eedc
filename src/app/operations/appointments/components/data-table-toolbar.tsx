import { Table } from '@tanstack/react-table';
import { Plus, ListFilter, CircleUserRound, ArrowLeftRight, Anchor, Calendar, Text } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Button } from '@/components/ui/button';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';
import { DataTableCompactFilter } from '@/components/data-table/data-table-compact-filter';
import { DataTableSearch } from '@/components/data-table/data-table-search';
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { DataTableFilterCommand } from '@/components/data-table/data-table-filter-command';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

function getPortFunctionValues<TData>(table: Table<TData>) {
	const portFunctionColumn = table.getColumn('portFunction');
	const columnValues = portFunctionColumn
		? Array.from(new Set((portFunctionColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getPortValues<TData>(table: Table<TData>) {
	const portNameColumn = table.getColumn('portName');
	const columnValues = portNameColumn
		? Array.from(new Set((portNameColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getEtaValues<TData>(table: Table<TData>) {
	const etaColumn = table.getColumn('eta');
	const columnValues = etaColumn
		? Array.from(new Set((etaColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getRequestDateValues<TData>(table: Table<TData>) {
	const requestDateColumn = table.getColumn('requestDate');
	const columnValues = requestDateColumn
		? Array.from(new Set((requestDateColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

function getNominationTypeValues<TData>(table: Table<TData>) {
	const nominationTypeColumn = table.getColumn('nominationType');
	const columnValues = nominationTypeColumn
		? Array.from(new Set((nominationTypeColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: 'Full agency',
		value,
		icon: undefined
	}));
}

function getOperatorValues<TData>(table: Table<TData>) {
	const operatorColumn = table.getColumn('operatorName');
	const columnValues = operatorColumn
		? Array.from(new Set((operatorColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;
	const navigate = useNavigate();

	const dropdownMenuFilter = (
		<DropdownMenuContent align="start" className="min-w-48">
			<DropdownMenuLabel>Filters</DropdownMenuLabel>
			<DropdownMenuSeparator />
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<ArrowLeftRight className="mr-1 size-3.5" />
					Port function
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('portFunction')}
							title="Port function"
							options={getPortFunctionValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Anchor className="mr-1 size-3.5" />
					Port
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('portName')}
							title="Port"
							options={getPortValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Calendar className="mr-1 size-3.5" />
					ETA
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('eta')}
							title="ETA"
							options={getEtaValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Text className="mr-1 size-3.5" />
					Nomination type
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('nominationType')}
							title="Nomination type"
							options={getNominationTypeValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Calendar className="mr-1 size-3.5" />
					Requested on
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('requestDate')}
							title="Requested on"
							options={getRequestDateValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleUserRound className="mr-1 size-3.5" />
					Nominated by
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('operatorName')}
							title="Operator"
							options={getOperatorValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
		</DropdownMenuContent>
	);

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center">
				<Button size="xs" className="mr-2 hidden" onClick={() => navigate('/operations/appointments/new-ai')}>
					<Plus />
					Appointment
				</Button>

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						{isFiltered ? (
							<Button variant="ghost" size="xs" className="w-0 px-0"></Button>
						) : (
							<Button variant="ghost" size="xs">
								<ListFilter />
								Filter
							</Button>
						)}
					</DropdownMenuTrigger>
					{dropdownMenuFilter}
				</DropdownMenu>

				{table.getColumn('portFunction') && (
					<DataTableCompactFilter
						column={table.getColumn('portFunction')}
						title="Port function"
						options={getPortFunctionValues(table)}
						icon={ArrowLeftRight}
					/>
				)}
				{table.getColumn('portName') && (
					<DataTableCompactFilter
						column={table.getColumn('portName')}
						title="Port"
						options={getPortValues(table)}
						icon={Anchor}
					/>
				)}
				{table.getColumn('eta') && (
					<DataTableCompactFilter
						column={table.getColumn('eta')}
						title="ETA"
						options={getEtaValues(table)}
						icon={Calendar}
					/>
				)}
				{table.getColumn('nominationType') && (
					<DataTableCompactFilter
						column={table.getColumn('nominationType')}
						title="Nomination type"
						options={getNominationTypeValues(table)}
						icon={Text}
					/>
				)}
				{table.getColumn('requestDate') && (
					<DataTableCompactFilter
						column={table.getColumn('requestDate')}
						title="Requested on"
						options={getRequestDateValues(table)}
						icon={Calendar}
					/>
				)}
				{table.getColumn('operatorName') && (
					<DataTableCompactFilter
						column={table.getColumn('operatorName')}
						title="Nominated by"
						options={getOperatorValues(table)}
						icon={CircleUserRound}
					/>
				)}

				{isFiltered && (
					<TooltipProvider>
						<DropdownMenu>
							<Tooltip>
								<TooltipTrigger asChild>
									<DropdownMenuTrigger asChild>
										<Button variant="ghost" size="icon" className="h-7 w-7">
											<ListFilter />
										</Button>
									</DropdownMenuTrigger>
								</TooltipTrigger>
								<TooltipContent>Filter</TooltipContent>
							</Tooltip>
							{dropdownMenuFilter}
						</DropdownMenu>
					</TooltipProvider>
				)}
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
