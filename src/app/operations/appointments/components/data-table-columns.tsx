import { ColumnDef } from '@tanstack/react-table';
import { Box, Clock } from 'lucide-react';
import { operators } from '../data/data';
import { appointment } from '../data/schema';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { formatDateTime } from '@/common/utils/formatUtils';

export const columns: ColumnDef<appointment>[] = [
	{
		accessorKey: 'fileId',
		meta: {
			label: 'Voyage ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Voyage ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('fileId')}</div>
	},
	{
		accessorKey: 'vesselName',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => {
			const vessel = row.original;
			return <span className="font-medium">{vessel.vesselName}</span>;
		}
	},
	{
		accessorKey: 'portFunction',
		meta: {
			label: 'Activity'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Activity" />,
		cell: ({ row }) => {
			// const portFunction = portFunctions.find(
			// 	portFunction => portFunction.value === row.getValue('portFunction')
			// );

			// if (!portFunction) {
			// 	return null;
			// }

			// return (
			// 	<Badge variant="secondary" className="rounded-full">
			// 		{portFunction.icon && <portFunction.icon className={cn('h-3 w-3', portFunction.color)} />}
			// 		<span>{portFunction.label}</span>
			// 	</Badge>
			// );

			const rowIndex = row.index;
			const nominationTypes = ['Loading', 'Discharging'];
			const selectedType = nominationTypes[rowIndex % 2];

			return (
				<Badge variant="secondary" className="text-muted-foreground rounded-full">
					<Box className="text-primary size-3" />
					{selectedType}
				</Badge>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'portName',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" />,
		cell: ({ row }) => {
			const port = row.original;

			if (!port) {
				return null;
			}

			return (
				<span>
					{port.portName}, {port.portCountryCode}
				</span>
			);
		},

		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'eta',
		meta: {
			label: 'ETA'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA" />,
		cell: ({ row }) => <div className="text-muted-foreground text-sm">{formatDateTime(row.getValue('eta'))}</div>,

		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'appointmentStatus',
		meta: {
			label: 'Appointment status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Appointment status" />,
		cell: () => (
			<Badge variant="outline" className="rounded-full">
				<Clock className="size-3 text-amber-500" />
				<span className="text-muted-foreground">Pending</span>
			</Badge>
		),
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'agentName',
		meta: {
			label: 'Appointed agent'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Appointed agent" />,
		cell: ({ row }) => (
			<div className="max-w-[200px] truncate" title={row.getValue('agentName')}>
				{row.getValue('agentName')}
			</div>
		),

		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'nominationType',
		meta: {
			label: 'Nomination type'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Nomination type" />,
		cell: ({ row }) => {
			const rowIndex = row.index;
			const nominationTypes = ["Charterer's nominated", "Owner's nominated", "Owner's protective", 'HUB agent'];
			const selectedType = nominationTypes[rowIndex % 4];

			return (
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					{selectedType}
				</Badge>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'requestDate',
		meta: {
			label: 'Requested on'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Requested on" />,
		cell: ({ row }) => <div className="text-muted-foreground">{formatDateTime(row.getValue('requestDate'))}</div>,

		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'operatorName',
		meta: {
			label: 'Appointed by'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Appointed by" />,
		cell: ({ row }) => {
			const operatorName = row.getValue('operatorName');
			const operator = operators.find(operator => operator.value === operatorName) || {
				value: operatorName,
				label: (operatorName as string)
					.split(' ')
					.map((part, index) => (index === 0 ? `${part.charAt(0)}. ` : part))
					.join(''),
				short: '-',
				avatar: '/static/media/avatar-md.webp'
			};

			return (
				<div className="flex items-center gap-2">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src={operator.avatar} alt={operator.label} />
						<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
							JD
						</AvatarFallback>
					</Avatar>
					<span className="text-muted-foreground max-w-[120px] truncate text-sm">{operator.label}</span>
				</div>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	}
];
