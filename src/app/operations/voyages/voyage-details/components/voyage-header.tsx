import { CircleDashed, PenLine } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface VoyageHeaderProps {
	className?: string;
}

export default function VoyageHeader({ className }: VoyageHeaderProps) {
	const navigate = useNavigate();

	return (
		<div className={`flex items-center gap-4 ${className}`}>
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">mv Suez Navigator</h2>
				<div className="text-muted-foreground">/</div>
				<div className="text-muted-foreground text-sm font-medium">#VOY-LA-01</div>
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<CircleDashed className="size-3 text-emerald-500" />
					<span>Commenced</span>
				</Badge>
			</div>
			<Button variant="outline" size="xs" onClick={() => navigate(`/operations/voyages/new`)}>
				<PenLine />
				Edit
			</Button>
		</div>
	);
}
