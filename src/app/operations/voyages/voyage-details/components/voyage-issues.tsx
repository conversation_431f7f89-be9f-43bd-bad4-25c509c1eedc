import { useState, useEffect } from 'react';
import { TriangleAlert, ChevronRight, LoaderCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Button } from '@/components/ui/button';

interface IssuesData {
	row_number: number;
	Id: number;
	Date: string;
	Description: string;
}

interface ApiResponse {
	issues: IssuesData[];
}

export default function VoyageIssues() {
	const [issuesData, setIssuesData] = useState<IssuesData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchIssuesData = async () => {
			try {
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/issues.json');
				if (!response.ok) {
					throw new Error('Failed to fetch issues data');
				}
				const data = (await response.json()) as ApiResponse[];
				setIssuesData(data[0]?.issues || []);
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		void fetchIssuesData();
	}, []);

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading issues data...</div>
			</div>
		);
	}

	if (error) {
		return <div className="grid gap-6 text-red-500">Error: {error}</div>;
	}

	return (
		<div className="flex flex-col gap-2 py-4">
			<div className="grid gap-2">
				{issuesData.map(issue => (
					<Button
						key={issue.row_number}
						variant="outline"
						className="group h-auto justify-start gap-3 rounded-lg p-3"
					>
						<TriangleAlert className="size-4 text-red-500" />
						<div className="flex-1 text-left font-normal text-wrap">
							<div className="ai-markdown text-md flex-1 text-justify leading-relaxed font-normal">
								<ReactMarkdown remarkPlugins={[remarkGfm]}>{issue.Description}</ReactMarkdown>
							</div>
						</div>
						<div className="text-muted-foreground text-xs">{issue.Date}</div>
						<ChevronRight className="text-muted-foreground opacity-0 group-hover:opacity-100" />
					</Button>
				))}
			</div>
		</div>
	);
}
