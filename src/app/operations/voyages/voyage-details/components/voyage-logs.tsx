import { useState, useEffect } from 'react';
import { ChevronRight, LoaderCircle, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LogsData {
	row_number: number;
	Date: string;
	Description: string;
}

interface ApiResponse {
	logs: LogsData[];
}

export default function VoyageLogs() {
	const [logsData, setLogsData] = useState<LogsData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchLogsData = async () => {
			try {
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/logs.json');
				if (!response.ok) {
					throw new Error('Failed to fetch logs data');
				}
				const data = (await response.json()) as ApiResponse[];
				setLogsData(data[0]?.logs || []);
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		void fetchLogsData();
	}, []);

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading logs data...</div>
			</div>
		);
	}

	if (error) {
		return <div className="grid gap-6 text-red-500">Error: {error}</div>;
	}

	return (
		<div className="flex flex-col gap-2 py-4">
			<div className="grid gap-2">
				{logsData.map(log => (
					<Button
						key={log.row_number}
						variant="outline"
						className="bg-panel/50 hover:bg-panel group h-auto justify-start gap-3 rounded-lg p-3"
					>
						<Calendar className="text-muted-foreground size-4" />
						<div className="text-muted-foreground text-xs">{log.Date}</div>
						<div className="flex-1 text-left font-normal text-wrap">{log.Description}</div>
						<ChevronRight className="text-muted-foreground opacity-0 group-hover:opacity-100" />
					</Button>
				))}
			</div>
		</div>
	);
}
