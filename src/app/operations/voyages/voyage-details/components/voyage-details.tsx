import { useEffect, useState } from 'react';
import { Anchor, Hash, Ship, User, Building2, CircleDashed, Calendar, LoaderCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

interface RecapData {
	row_number: number;
	index: number;
	owner_name: string;
	owner_broker_name: string;
	charterer_name: string;
	charterer_broker_name: string;
	cp_date: string;
	cp_form_name: string;
	cp_place: string;
	layday: string;
	cancelling: string;
	vessel_name: string;
	vessel_imo: string;
	cargo_name_recap: string;
	cargo_name: string;
	cargo_quantity: string;
	cargo_unit: string;
	loadports_list: string;
	loadport_safe_berth: string;
	loadport_safe_berth_min: string;
	loadport_safe_berth_max: string;
	loadport_safe_anchorage_name: string;
	loadport_safe_anchorage_min: string;
	loadport_safe_anchorage_max: string;
	loadport_safe_port_name: string;
	loadport_safe_port_min: string;
	loadport_safe_port_max: string;
	loadport_naabsa: string;
	loadport_name_recap: string;
	loadport_name: string;
	loadport_terms_recap: string;
	loadport_allowed_time_value: number;
	loadport_allowed_time_unit: string;
	loadport_allowed_time_option: string;
	loadport_cargo_terms: string;
	load_nor_clause: string;
	load_turn_time: string;
	discharge_port_1_recap: string;
	discharge_port_1: string;
	discharge_port_2_recap: string;
	discharge_port_2: string;
	discharge_port_1_terms_recap: string;
	discharge_port_1_nor_clause: string;
	discharge_port_1_turn_time: string;
	discharge_port_2_terms_recap: string;
	discharge_port_2_nor_clause: string;
	discharge_port_2_turn_time: string;
	demurrage: string;
	despatch: string;
	freight: string;
	agents_at_load: string;
	agents_at_discharge: string;
	opening_port: string;
	opening_port_date: string;
}

interface ApiResponse {
	recap: RecapData[];
}

export default function VoyageDetails() {
	const [recapData, setRecapData] = useState<RecapData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchRecapData = async () => {
			try {
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/recap.json');
				if (!response.ok) {
					throw new Error('Failed to fetch recap data');
				}
				const data = (await response.json()) as ApiResponse[];
				setRecapData(data[0]?.recap || []);
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		void fetchRecapData();
	}, []);

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading recap data...</div>
			</div>
		);
	}

	if (error) {
		return <div className="grid gap-6 text-red-500">Error: {error}</div>;
	}

	return (
		<div className="grid gap-6">
			{recapData.map(recap => (
				<div key={recap.index || recap.row_number} className="grid gap-6">
					<div className="flex w-full flex-col items-center gap-16 pt-4 lg:flex-row">
						<div className="w-full flex-1">
							<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
								<div className="text-muted-foreground flex items-center gap-2">
									<Hash className="size-4" />
									Voyage ID
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium">EM-2935-CN</span>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<CircleDashed className="size-3 text-emerald-500" />
										<span>Commenced</span>
									</Badge>
								</div>
								<div className="text-muted-foreground flex items-center gap-2">
									<Ship className="size-4" />
									Vessel
								</div>
								<div className="flex items-center gap-2">
									<div className="font-medium">mv {recap.vessel_name}</div>
									<div className="text-muted-foreground">
										{recap.vessel_imo && `(${recap.vessel_imo})`}
									</div>
								</div>
								<div className="text-muted-foreground flex items-center gap-2">
									<Anchor className="size-4" />
									Opening port
								</div>
								<div className="font-medium">{recap.opening_port}</div>
							</div>
						</div>
						<div className="w-full flex-1">
							<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
								<div className="text-muted-foreground flex items-center gap-2">
									<Building2 className="size-4" />
									Legal entity
								</div>
								<div className="font-medium">LG Panama Ltd.</div>
								<div className="text-muted-foreground flex items-center gap-2">
									<User className="size-4" />
									Operator
								</div>
								<div className="flex items-center gap-2">
									<Avatar className="size-5 rounded-sm">
										<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
										<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
											JD
										</AvatarFallback>
									</Avatar>
									<span className="font-medium">J. Todorov</span>
								</div>
								<div className="text-muted-foreground flex items-center gap-2">
									<Calendar className="size-4" />
									Opening date
								</div>
								<div className="font-medium">{recap.opening_port_date}</div>
							</div>
						</div>
					</div>
					<Separator />
					<h2 className="text-lg font-medium">Structured Recap</h2>
					<div className="grid grid-cols-2 items-center gap-x-16 text-sm">
						{Object.entries(recap).map(([key, value]) => {
							// Skip empty values
							if (!value && value !== 0) return null;

							// Skip index and row_number
							if (key === 'index' || key === 'row_number') return null;

							const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

							return (
								<div key={key} className="flex h-full items-center gap-4 border-b py-2">
									<span className="text-muted-foreground max-w-[120px] min-w-[120px]">
										{formattedKey}
									</span>
									<span className="font-medium">{value}</span>
								</div>
							);
						})}
					</div>
				</div>
			))}
		</div>
	);
}
