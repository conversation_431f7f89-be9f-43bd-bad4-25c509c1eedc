import { <PERSON><PERSON><PERSON>, MoveUp, Spark<PERSON> } from 'lucide-react';
import VoyageMap from './voyage-map';
import VoyageSummary2 from './voyage-summary2';
import VoyageAlerts from './voyage-issues';
import VoyagePending from './voyage-pending';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function VoyageGeneral() {
	return (
		<div className="grid gap-6">
			<div className="relative grid">
				<div className="from-background to-background/0 absolute top-0 z-10 w-full bg-linear-to-b from-30%">
					<div className="m-auto flex w-full max-w-4xl items-center justify-between gap-12 py-4">
						<div className="flex flex-1 items-center justify-between gap-4 px-0 py-1">
							<Button variant="ghost" className="h-auto flex-col items-start gap-0 p-1 text-xs">
								Las Palmas, ES
								<div className="text-muted-foreground text-xs font-normal">11 Apr, 08:00</div>
							</Button>
							<ArrowRight className="text-muted-foreground size-3" />
							<Button variant="ghost" className="h-auto flex-col items-start gap-0 p-1 text-xs">
								Port Said, EG
								<div className="text-muted-foreground text-xs font-normal">17 Apr, 10:00</div>
							</Button>
							<ArrowRight className="text-muted-foreground size-3" />
							<Button variant="ghost" className="h-auto flex-col items-start gap-0 p-1 text-xs">
								Alappuzha, IN
								<div className="text-muted-foreground text-xs font-normal">28 Apr, 08:00</div>
							</Button>
						</div>
						<div className="flex flex-1 items-center justify-end gap-2 rounded-xl p-2 text-sm">
							<div className="font-medium">17d : 3h</div>
							<Separator orientation="vertical" className="h-4" />
							<div className="font-medium">5,023 nm</div>
							<Separator orientation="vertical" className="h-4" />
							<div className="flex items-center gap-1 font-semibold">
								$476,280
								<MoveUp className="size-3 text-red-500" />
							</div>
						</div>
					</div>
				</div>
				<VoyageMap className="h-[350px] rounded-none" />
				<div className="bg-background/80 relative m-auto -mt-14 w-full max-w-4xl rounded-xl border-t backdrop-blur">
					<div className="flex items-center gap-2 p-4 text-base font-semibold">
						<Sparkles className="text-primary size-4" />
						Operator One
					</div>
					<div className="grid grid-cols-2 gap-4 px-4 pb-6">
						<VoyageSummary2 />
						<div className="flex flex-col gap-4 pb-4">
							<VoyageAlerts />
							<VoyagePending />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
