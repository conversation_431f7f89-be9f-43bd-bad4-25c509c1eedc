import { ChevronDown, Download, Files } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

const voyageDocuments = [
	{
		id: '1',
		name: 'Voyage general terms.pdf',
		uploadedAt: '08 Jun 24 - 10:53',
		uploadedBy: 'J. Doe',
		type: 'pdf'
	},
	{
		id: '2',
		name: 'Charter party.pdf',
		uploadedAt: '08 Jun 24 - 10:53',
		uploadedBy: '<PERSON>. <PERSON>',
		type: 'pdf'
	}
];

const portDocuments = [
	{
		id: '1',
		name: 'Loadport Docs.pdf',
		uploadedAt: '08 Jun 24 - 10:53',
		uploadedBy: '<PERSON><PERSON> <PERSON><PERSON>',
		type: 'pdf'
	}
];

export default function VoyageDocuments() {
	return (
		<div className="grid gap-4 py-4">
			<div className="flex flex-col gap-2">
				<div className="text-muted-foreground text-sm">
					<Button variant="ghost" className="h-auto gap-1 px-2 py-1 font-normal">
						Voyage documents
						<ChevronDown />
					</Button>
				</div>
				{voyageDocuments.map(document => (
					<div key={document.id} className="bg-panel/50 hover:bg-panel cursor-pointer rounded-lg border p-3">
						<div className="flex items-center gap-3">
							<Badge variant="default" className="h-7 w-7 justify-center rounded-md p-1.5">
								<Files className="text-primary-foreground size-3.5" />
							</Badge>
							<div className="text-md flex-1">{document.name}</div>
							<div className="flex items-center justify-end gap-4">
								<div className="flex items-center justify-end gap-2">
									<div className="text-muted-foreground text-xs">{document.uploadedAt}</div>
									<Avatar className="size-5 rounded-sm">
										<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
										<AvatarFallback className="bg-primary text-2xs text-primary-foreground rounded-md font-medium">
											JD
										</AvatarFallback>
									</Avatar>
								</div>
								<Separator orientation="vertical" className="h-4" />
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="text-muted-foreground h-7 w-7"
											>
												<Download />
											</Button>
										</TooltipTrigger>
										<TooltipContent>Download</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</div>
						</div>
					</div>
				))}
			</div>
			<Separator className="bg-transparent" />
			<div className="flex flex-col gap-2">
				<div className="text-muted-foreground text-sm">
					<Button variant="ghost" className="h-auto gap-1 px-2 py-1 font-normal">
						Durban, SA (#ABX-123-NL)
						<ChevronDown />
					</Button>
				</div>
				{portDocuments.map(document => (
					<div key={document.id} className="bg-panel cursor-pointer rounded-lg border p-3">
						<div className="flex items-center gap-3">
							<Badge variant="default" className="h-7 w-7 justify-center rounded-md p-1.5">
								<Files className="text-primary-foreground size-3.5" />
							</Badge>
							<div className="text-md flex-1">{document.name}</div>
							<div className="flex items-center justify-end gap-4">
								<div className="flex items-center justify-end gap-2">
									<div className="text-muted-foreground text-xs">{document.uploadedAt}</div>
									<Avatar className="size-5 rounded-sm">
										<AvatarFallback className="bg-primary text-2xs text-primary-foreground rounded-md font-medium">
											JD
										</AvatarFallback>
									</Avatar>
								</div>
								<Separator orientation="vertical" className="h-4" />
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="text-muted-foreground h-7 w-7"
											>
												<Download />
											</Button>
										</TooltipTrigger>
										<TooltipContent>Download</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
