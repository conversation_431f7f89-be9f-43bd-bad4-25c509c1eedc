import { useNavigate } from 'react-router';
import { useEffect, useState } from 'react';

import { <PERSON><PERSON>he<PERSON>, CircleDashed, CircleDot, LoaderCircle } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TooltipProvider } from '@/components/ui/tooltip';
import { formatDateTime, formatAmountSimple, formatAmount } from '@/common/utils/formatUtils';

interface ItineraryData {
	row_number: number;
	index: number;
	status: string;
	port_name: string;
	function: string;
	arrival_time: string;
	berthing_time: string;
	departure_time: string;
	port_cost: number;
	currency: string;
	port_cost_status: string;
	port_cost_payment_date: string;
	estimated_port_stay: number;
	actual_port_stay: number;
	port_stay_difference: number;
}

interface ApiResponse {
	itinerary: ItineraryData[];
}

export default function VoyageItineraryMvp() {
	const navigate = useNavigate();
	const [itineraryData, setItineraryData] = useState<ItineraryData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchItineraryData = async () => {
			try {
				setLoading(true);
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/itinerary.json');

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = (await response.json()) as ApiResponse[];

				// Extract itinerary data from the first item in the array
				if (data && data.length > 0 && data[0].itinerary) {
					console.log('API data structure:', data[0].itinerary[0]); // Debug log
					setItineraryData(data[0].itinerary);
				} else {
					setError('No itinerary data found');
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'Failed to fetch itinerary data');
				console.error('Error fetching itinerary data:', err);
			} finally {
				setLoading(false);
			}
		};

		void fetchItineraryData();
	}, []);

	const safeFormatDateTime = (dateString: string | null | undefined): string => {
		if (!dateString || dateString.trim() === '' || dateString === '---') {
			return '---';
		}

		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return '---';
			}
			return formatDateTime(dateString);
		} catch (error) {
			console.warn('Error formatting date:', dateString, error);
			return '---';
		}
	};

	const getStatusBadge = (status: string) => {
		const normalizedStatus = status.toLowerCase();

		if (normalizedStatus === 'sailed') {
			return (
				<Badge variant="outline" className="rounded-full bg-blue-500/10">
					<CircleCheck className="text-primary size-3" />
					Sailed
				</Badge>
			);
		} else if (normalizedStatus === 'arrived') {
			return (
				<Badge variant="outline" className="rounded-full bg-green-500/10">
					<CircleDot className="size-3 text-emerald-500" />
					Arrived
				</Badge>
			);
		} else if (normalizedStatus === 'planned') {
			return (
				<Badge variant="outline" className="rounded-full">
					<CircleDashed className="size-3" />
					Planned
				</Badge>
			);
		}

		return (
			<Badge variant="outline" className="max-w-28 truncate rounded-full">
				{status}
			</Badge>
		);
	};

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading itinerary data...</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="grid gap-4">
				<div className="py-8 text-center text-red-500">Error: {error}</div>
			</div>
		);
	}

	return (
		<TooltipProvider>
			<div className="grid gap-4">
				<Table className="border-none">
					<TableHeader className="border-none">
						<TableRow className="border-t">
							<TableHead className="text-xs text-nowrap"></TableHead>
							<TableHead className="text-xs text-nowrap">Status</TableHead>
							<TableHead className="px-4 text-xs text-nowrap">Port</TableHead>
							<TableHead className="text-xs text-nowrap">Function</TableHead>
							<TableHead className="border-r border-l text-xs text-nowrap">Arrival</TableHead>
							<TableHead className="border-r text-xs text-nowrap">Berthing</TableHead>
							<TableHead className="border-r text-xs text-nowrap">Departure</TableHead>
							<TableHead className="border-r px-4 text-right text-xs text-nowrap">Port Costs</TableHead>
							<TableHead className="border-r text-xs text-nowrap">Cost Status</TableHead>
							<TableHead className="border-r text-xs text-nowrap">Port Stay</TableHead>
							<TableHead className="border-r text-xs text-nowrap">Actual Port Stay</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody className="border-b">
						{itineraryData.map((item, index) => (
							<TableRow key={item.index || index} className="border-none">
								<TableCell></TableCell>
								<TableCell>{getStatusBadge(item.status)}</TableCell>
								<TableCell>
									<Button
										variant="ghost"
										className="h-auto px-2 py-1 font-medium"
										onClick={() => navigate('/operations/port-calls/123')}
									>
										{item.port_name}
									</Button>
								</TableCell>
								<TableCell>
									<div className="text-nowrap">
										<Badge variant="secondary" className="rounded-full">
											{item.function}
										</Badge>
									</div>
								</TableCell>
								<TableCell className="border-r border-l">
									<div className="text-nowrap">{safeFormatDateTime(item.arrival_time)}</div>
								</TableCell>
								<TableCell className="border-r">
									<div className="text-nowrap">{safeFormatDateTime(item.berthing_time)}</div>
								</TableCell>
								<TableCell className="border-r">
									<div className="text-nowrap">{safeFormatDateTime(item.departure_time)}</div>
								</TableCell>
								<TableCell className="border-r border-l text-right">
									{item.port_cost > 0 && (
										<Button variant="ghost" className="h-auto px-2 py-1">
											<div>
												<span className="text-muted-foreground text-2xs uppercase">
													{item.currency}
												</span>{' '}
												<span className="font-semibold">
													{formatAmountSimple(item.port_cost)}
												</span>
											</div>
										</Button>
									)}
								</TableCell>
								<TableCell className="border-r">
									{item.port_cost_status && (
										<Badge variant="outline" className="rounded-full">
											{item.port_cost_status}
										</Badge>
									)}
								</TableCell>
								<TableCell className="border-r">
									{item.estimated_port_stay && (
										<div>
											<span className="font-semibold">
												{formatAmount(item.estimated_port_stay)}
											</span>{' '}
											<span className="text-muted-foreground">days</span>
										</div>
									)}
								</TableCell>
								<TableCell className="border-r">
									{item.actual_port_stay && (
										<div>
											<span className="font-semibold">{formatAmount(item.actual_port_stay)}</span>{' '}
											<span className="text-muted-foreground">days</span>
										</div>
									)}
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
		</TooltipProvider>
	);
}
