import { useState } from 'react';
import { Sparkles } from 'lucide-react';
import Voyage from './components/voyage';
import VoyageChatDrawer from './components/voyage-chat-drawer';
import { data } from './data/data';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger, SidebarProvider, Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';
import Chat from '@/app/mapbox/components/chat/chat';
import { Drawer, DrawerTrigger } from '@/components/ui/drawer';
import ChatbotSimple from '@/components/chatbot/chatbot-simple';

export default function VoyagePage() {
	const [open, setOpen] = useState(false);
	const [drawerOpen, setDrawerOpen] = useState(false);

	return (
		<Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="../">Voyages</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>#VOY-LA-01</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex items-center justify-end gap-2 px-4">
					<ModeToggle />
					<Separator orientation="vertical" className="mx-2 h-4" />
					<Button variant="ghost" size="xs" onClick={() => setOpen(open => !open)}>
						<Sparkles />
						<span>Operator One</span>
					</Button>
				</div>
			</header>

			<SidebarProvider
				style={{ '--sidebar-width': '30rem' } as React.CSSProperties}
				open={open}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden"
				onOpenChange={setOpen}
			>
				<Voyage />
				<div className="from-background to-background/0 absolute right-0 bottom-0 left-0 flex hidden w-full items-center justify-center bg-linear-to-t from-30%">
					<div className="w-full max-w-3xl shadow-2xl">
						<DrawerTrigger className="w-full">
							<ChatbotSimple />
						</DrawerTrigger>
					</div>
				</div>
				<Sidebar variant="floating" side="right">
					<SidebarContent className="bg-background flex flex-col gap-0">
						<SidebarTrigger className="-ml-1 hidden" />
						<Chat
							data={data}
							backButton={false}
							chatTitle="mv Suez Navigator"
							collapseButton
							expandButton
							onExpandPanel={() => {
								setDrawerOpen(true);
							}}
							onOpenVoyagePanel={() => setOpen(false)}
						/>
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
			<VoyageChatDrawer />
		</Drawer>
	);
}
