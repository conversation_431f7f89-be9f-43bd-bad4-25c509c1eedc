/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
/* eslint-disable @typescript-eslint/no-unsafe-call */

import { ColumnDef } from '@tanstack/react-table';
import { AlertTriangleIcon, CircleDashed, MoveDown, Clock, ArrowRight } from 'lucide-react';
import { Voyage } from '../data/schema';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

// Array of ports with their country codes
const ports = [
	{ name: 'Rotterdam', code: 'nl' },
	{ name: 'Hamburg', code: 'de' },
	{ name: 'Antwerp', code: 'be' },
	{ name: 'Southampton', code: 'gb' },
	{ name: 'Barcelona', code: 'es' },
	{ name: 'Amsterdam', code: 'nl' },
	{ name: '<PERSON><PERSON><PERSON>', code: 'bg' },
	{ name: 'Dubai', code: 'ae' },
	{ name: 'Singapore', code: 'sg' },
	{ name: 'Shanghai', code: 'cn' },
	{ name: 'Busan', code: 'kr' },
	{ name: 'Piraeus', code: 'gr' },
	{ name: 'New York', code: 'us' },
	{ name: 'Los Angeles', code: 'us' },
	{ name: 'Port Said', code: 'eg' }
];

export const columns: ColumnDef<Voyage>[] = [
	{
		accessorKey: 'id',
		meta: {
			label: 'Voyage ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Voyage ID" />,
		cell: ({ row }) => (
			<div className="text-muted-foreground">
				#VOY-{(row.getValue('id') as string).slice(-4).toLocaleUpperCase()}
			</div>
		)
	},
	{
		accessorKey: 'vessel',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => <div className="font-medium">{row.getValue('vessel')}</div>
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			// Generate a deterministic random status based on the row ID
			const id = row.getValue('id') as string;
			// Use the last character of the ID to determine status for better distribution
			const lastChar = id.charAt(id.length - 1);
			const charCode = lastChar.charCodeAt(0);
			const isCommenced = charCode % 2 === 0;

			return isCommenced ? (
				<Badge variant="outline" className="rounded-full">
					<CircleDashed className="size-3 text-emerald-500" /> Commenced
				</Badge>
			) : (
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<Clock className="size-3" /> Scheduled
				</Badge>
			);
		}
	},
	{
		accessorKey: 'loadPort',
		meta: {
			label: 'Load Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Load Port" />,
		cell: ({ row }) => {
			// Generate a deterministic port based on the row ID
			const id = row.getValue('id') as string;
			const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
			// Select a random port from the ports array
			const port = ports[hash % ports.length];

			return (
				<div>
					{port.name}, {port.code.toUpperCase()}
				</div>
			);
		}
	},
	{
		accessorKey: 'otherPorts',
		meta: {
			label: '',
			className: 'text-center'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="" className="min-w-[80px]" />,
		cell: ({ row }) => {
			// Show Badge on every 4th row, ArrowRight on others
			const showBadge = row.index % 4 === 3; // Every 4th row (0-indexed, so 3, 7, 11, etc.)

			return (
				<div className="flex items-center gap-1">
					<Separator className="w-6" />
					{showBadge ? (
						<Badge
							variant="secondary"
							className="text-muted-foreground h-4 w-4 justify-center rounded-full px-0 text-xs"
						>
							2
						</Badge>
					) : (
						<ArrowRight className="text-muted-foreground size-3.5 opacity-50" />
					)}

					<Separator className="w-6" />
				</div>
			);
		}
	},
	{
		accessorKey: 'dischargePort',
		meta: {
			label: 'Discharge Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Discharge Port" />,
		cell: ({ row }) => {
			// Generate a deterministic port based on the row ID
			const id = row.getValue('id') as string;
			const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
			// Select a different port for the next port (offset by 5 to ensure it's different from current port)
			const port = ports[(hash + 5) % ports.length];

			return (
				<div>
					{port.name}, {port.code.toUpperCase()}
				</div>
			);
		}
	},
	{
		accessorKey: 'cargo',
		meta: {
			label: 'Cargo'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Cargo" />,
		cell: () => (
			<div className="flex items-center gap-2">
				<div>12,500 mts</div>
				<div className="text-muted-foreground">-</div>
				<div className="text-muted-foreground">Iron Ore</div>
			</div>
		)
	},
	{
		accessorKey: 'progress',
		meta: {
			label: 'Progress'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Progress" />,
		cell: ({ row }) => {
			// Generate a deterministic random value based on the row ID
			const id = row.getValue('id') as string;
			// Use the ID to generate a number between 20 and 80
			const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
			const progressValue = 20 + (hash % 61); // Value between 20 and 80
			return <Progress value={progressValue} className="w-full min-w-[120px]" />;
		}
	},
	{
		accessorKey: 'pnl',
		meta: {
			label: 'P&L',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="P&L" reverse />,
		cell: () => (
			<div className="flex items-center justify-end gap-1 px-2">
				<div className="text-right font-medium">
					<span className="text-muted-foreground text-2xs">USD</span> 126,540
				</div>
				<MoveDown className="size-3 text-red-500" />
			</div>
		)
	},
	{
		accessorKey: 'operator',
		meta: {
			label: 'Operator'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Operator" />,
		cell: () => (
			<div className="flex items-center gap-2">
				<Avatar className="h-5 w-5 rounded-sm">
					<AvatarImage src="" alt="" />
					<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
						JD
					</AvatarFallback>
				</Avatar>
				<span className="text-muted-foreground max-w-[120px] truncate text-sm">J. Doe</span>
			</div>
		)
	},
	{
		accessorKey: 'nextPort',
		meta: {
			label: 'Next Port',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Next Port" />,
		cell: ({ row }) => {
			// Generate a deterministic port based on the row ID
			const id = row.getValue('id') as string;
			const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 1);
			// Select a random port from the ports array
			const port = ports[hash % ports.length];

			return (
				<div>
					{port.name}, {port.code.toUpperCase()}
				</div>
			);
		}
	},
	{
		accessorKey: 'nextPortEta',
		meta: {
			label: 'Next Port ETA',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Next Port ETA" />,
		cell: () => <div className="text-muted-foreground">29 Apr - 13:45</div>
	},
	{
		accessorKey: 'startDate',
		meta: {
			label: 'Start Date',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Start Date" />,
		cell: () => <div className="text-muted-foreground">12 Apr - 18:00</div>
	},
	{
		accessorKey: 'completionDate',
		meta: {
			label: 'Completion Date',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Completion Date" />,
		cell: () => <div className="text-muted-foreground">18 Apr - 18:00</div>
	},
	{
		accessorKey: 'charterer',
		meta: {
			label: 'Charterer',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Charterer" />,
		cell: ({ row }) => (
			<div className="max-w-[200px] truncate" title={row.getValue('charterer')}>
				Glencore Grains B.V.
			</div>
		)
	},
	{
		accessorKey: 'alerts',
		meta: {
			label: 'Alerts',
			defaultHidden: true
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Alerts" reverse />,
		cell: () => (
			<div className="flex items-center justify-end gap-2 px-2">
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<AlertTriangleIcon className="size-3 text-red-500" />2
				</Badge>
			</div>
		)
	}
];
