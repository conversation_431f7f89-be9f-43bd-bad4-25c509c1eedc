import { Table } from '@tanstack/react-table';

import { Plus, Loader2, <PERSON>Filter, CircleDashed, Box, Anchor, CircleUserRound } from 'lucide-react';
import { Dispatch, SetStateAction, useState } from 'react';
import { useNavigate } from 'react-router';
import EstimateFormX2 from '../new-estimate/components/estimate-form-x2';
import { Voyage } from '../data/schema';
import { cargoes, operators, ports, statuses } from '../data/data';
import {
	Drawer,
	DrawerContent,
	DrawerHeader,
	DrawerTitle,
	DrawerDescription,
	DrawerFooter
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { DataTableCompactFilter } from '@/components/data-table/data-table-compact-filter';
import { DataTableFilterCommand } from '@/components/data-table/data-table-filter-command';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';
import { DataTableSearch } from '@/components/data-table/data-table-search';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
	setData: Dispatch<SetStateAction<Voyage[]>>;
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const isFiltered = table.getState().columnFilters.length > 0;

	const navigate = useNavigate();

	const handleCalculateVoyage = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			// Using void operator to explicitly mark the promise as ignored
			void navigate('/operations/voyages/new-estimate/1');
		}, 500);
	};

	const dropdownMenuFilter = (
		<DropdownMenuContent align="start" className="min-w-48">
			<DropdownMenuLabel>Filters</DropdownMenuLabel>
			<DropdownMenuSeparator />
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleDashed className="mr-1 size-3.5" />
					Status
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand column={table.getColumn('status')} title="Status" options={statuses} />
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Box className="mr-1 size-3.5" />
					Cargo
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand column={table.getColumn('cargo')} title="Cargo" options={cargoes} />
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Anchor className="mr-1 size-3.5" />
					Port
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand column={table.getColumn('loadPort')} title="Port" options={ports} />
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleUserRound className="mr-1 size-3.5" />
					Operator
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('operator')}
							title="Operator"
							options={operators}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
		</DropdownMenuContent>
	);

	return (
		<Drawer>
			<div className="flex items-center justify-between border-t border-b py-2">
				<div className="flex flex-1 items-center space-x-2">
					<Button size="xs" onClick={() => void navigate('/operations/voyages/new')}>
						<Plus /> Voyage
					</Button>

					<div className="flex h-7 items-center overflow-hidden">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								{isFiltered ? (
									<Button variant="ghost" size="xs" className="w-0 px-0"></Button>
								) : (
									<Button variant="ghost" size="xs">
										<ListFilter />
										Filter
									</Button>
								)}
							</DropdownMenuTrigger>
							{dropdownMenuFilter}
						</DropdownMenu>
						{table.getColumn('status') && (
							<DataTableCompactFilter
								column={table.getColumn('status')}
								title="Status"
								options={statuses}
								icon={CircleDashed}
							/>
						)}
						{table.getColumn('cargo') && (
							<DataTableCompactFilter
								column={table.getColumn('cargo')}
								title="Cargo"
								options={cargoes}
								icon={Box}
							/>
						)}
						{table.getColumn('cargo') && (
							<DataTableCompactFilter
								column={table.getColumn('loadPort')}
								title="Port"
								options={ports}
								icon={Anchor}
							/>
						)}
						{table.getColumn('operator') && (
							<DataTableCompactFilter
								column={table.getColumn('operator')}
								title="Operator"
								options={operators}
								icon={CircleUserRound}
							/>
						)}
						{isFiltered && (
							<TooltipProvider>
								<DropdownMenu>
									<Tooltip>
										<TooltipTrigger asChild>
											<DropdownMenuTrigger asChild>
												<Button variant="ghost" size="icon" className="h-7 w-7">
													<ListFilter />
												</Button>
											</DropdownMenuTrigger>
										</TooltipTrigger>
										<TooltipContent>Filter</TooltipContent>
									</Tooltip>
									{dropdownMenuFilter}
								</DropdownMenu>
							</TooltipProvider>
						)}
					</div>
				</div>
				<div className="flex items-center gap-1">
					<DataTableSearch table={table} />
					<DataTableDisplayOptions table={table} />
				</div>
			</div>
			<DrawerContent className="data-[vaul-drawer-direction=bottom]:max-h-[90vh]">
				<div className="flex w-full flex-col overflow-hidden">
					<DrawerHeader className="m-auto w-full max-w-2xl">
						<DrawerTitle>Let&apos;s create a new voyage estimate</DrawerTitle>
						<DrawerDescription>Estimate Description</DrawerDescription>
					</DrawerHeader>
					<div className="flex-1 overflow-auto">
						<EstimateFormX2 />
					</div>
					<DrawerFooter className="m-auto w-full max-w-2xl">
						<Button size="sm" disabled={isLoading} onClick={handleCalculateVoyage}>
							{isLoading ? (
								<>
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Calculating...</span>
								</>
							) : (
								'Calculate'
							)}
						</Button>
					</DrawerFooter>
				</div>
			</DrawerContent>
		</Drawer>
	);
}
