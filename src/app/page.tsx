import { useNavigate } from 'react-router';
import { Plus } from 'lucide-react';
import HeadingWidget1 from './home/<USER>/heading-widget-1';
import AmountWidget1 from './home/<USER>/amount-widget-1';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';

export default function Page() {
	const navigate = useNavigate();

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>Home</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center overflow-auto p-4 pt-0">
				<div className="grid w-full max-w-5xl gap-6">
					<HeadingWidget1 className="col-span-12" />
					<div className="col-span-12 flex items-center justify-center">
						<Button variant="default" onClick={() => void navigate('/operations/voyages/new')}>
							<Plus />
							New Voyage
						</Button>
					</div>
					<AmountWidget1
						className="col-span-12 xl:col-span-4"
						title="Balance Exposure"
						amount="$128,231.89"
					/>
					<AmountWidget1 className="col-span-12 xl:col-span-4" title="Total Voyages" amount="9" />
					<AmountWidget1 className="col-span-12 xl:col-span-4" title="Average Port Stays" amount="7.5 days" />
				</div>
			</div>
		</>
	);
}
