import { LayerProps } from 'react-map-gl/mapbox';
import { MarkerData } from '../types';

// Map styles
export const lightStyle = 'mapbox://styles/mapbox/light-v11';
export const darkStyle = 'mapbox://styles/dankoto/cm82vv78l00ny01r319deb2fd';

// Marker data
export const markers: MarkerData[] = [
	{
		id: 'vessel_001',
		longitude: 121.8339, // Near Shanghai
		latitude: 31.2304,
		position: 45,
		status: 'enroute',
		vessel: 'mv Pacific Voyager',
		voyageId: 'VOY-MF-01',
		nextPort: 'Shanghai, CN',
		eta: '02 Mar - 10:00',
		cargo: '2,500.000 mts - Iron Ore',
		charterer: 'Global Chartering S.A.',
		info: 'ETA: Port Said, EG / 03 Mar - 14:00',
		alert: true,
		urgent: false,
		pnl: {
			amount: '-45,330.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_002',
		longitude: -74.006, // Near New York
		latitude: 40.7128,
		position: 0,
		status: 'anchored',
		vessel: 'mv Atlantic Guardian',
		voyageId: 'VOY-MF-02',
		nextPort: 'Boston, US',
		eta: '05 Mar - 09:00',
		cargo: '1,800.000 mts - Containers',
		charterer: 'American Shipping Co.',
		info: 'ATA: New York, US / 03 Mar - 15:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '32,450.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_003',
		longitude: 103.8198, // Near Singapore
		latitude: 1.3521,
		position: 180,
		status: 'enroute',
		vessel: 'mv Asian Enterprise',
		voyageId: 'VOY-MF-03',
		nextPort: 'Singapore, SG',
		eta: '04 Mar - 08:00',
		cargo: '3,200.000 mts - Palm Oil',
		charterer: 'Singapore Trading Ltd.',
		info: 'ETA: Singapore, SG / 04 Mar - 08:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '18,720.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_004',
		longitude: -79.9959, // Panama Canal
		latitude: 9.082,
		position: 0,
		status: 'anchored',
		vessel: 'mv Global Pioneer',
		voyageId: 'VOY-MF-04',
		nextPort: 'Cartagena, CO',
		eta: '07 Mar - 14:00',
		cargo: '2,100.000 mts - Mixed Cargo',
		charterer: 'Panama Logistics S.A.',
		info: 'ATA: Panama City, PA / 05 Mar - 12:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '-12,890.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_005',
		longitude: 31.2357, // Suez Canal
		latitude: 30.0444,
		position: 135,
		status: 'enroute',
		vessel: 'mv Suez Navigator',
		voyageId: 'VOY-MF-05',
		nextPort: 'Port Said, EG',
		eta: '03 Mar - 14:00',
		cargo: '2,800.000 mts - Crude Oil',
		charterer: 'Mediterranean Shipping Corp.',
		info: 'ETA: Port Said, EG / 03 Mar - 14:00',
		alert: true,
		urgent: false,
		pnl: {
			amount: '41,200.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_006',
		longitude: 151.2093, // Near Sydney
		latitude: -33.8688,
		position: 0,
		status: 'anchored',
		vessel: 'mv Southern Star',
		voyageId: 'VOY-MF-06',
		nextPort: 'Melbourne, AU',
		eta: '06 Mar - 11:00',
		cargo: '1,500.000 mts - Coal',
		charterer: 'Australian Resources Ltd.',
		info: 'ATA: Sydney, AU / 04 Mar - 09:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '-8,750.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_007',
		longitude: -58.3816, // Near Buenos Aires
		latitude: -34.6037,
		position: 315,
		status: 'enroute',
		vessel: 'mv South Atlantic Queen',
		voyageId: 'VOY-MF-07',
		nextPort: 'Buenos Aires, AR',
		eta: '05 Mar - 11:00',
		cargo: '2,300.000 mts - Grain',
		charterer: 'Argentina Exports S.A.',
		info: 'ETA: Buenos Aires, AR / 05 Mar - 11:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '25,600.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_008',
		longitude: 139.6503, // Near Tokyo
		latitude: 35.6762,
		position: 0,
		status: 'anchored',
		vessel: 'mv Orient Express',
		voyageId: 'VOY-MF-08',
		nextPort: 'Yokohama, JP',
		eta: '08 Mar - 13:00',
		cargo: '1,900.000 mts - Electronics',
		charterer: 'Japan Trading Co.',
		info: 'ATA: Tokyo, JP / 06 Mar - 16:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '14,300.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_009',
		longitude: 72.8777, // Near Mumbai
		latitude: 19.076,
		position: 90,
		status: 'enroute',
		vessel: 'mv Indian Ocean Trader',
		voyageId: 'VOY-MF-09',
		nextPort: 'Mumbai, IN',
		eta: '04 Mar - 13:00',
		cargo: '2,600.000 mts - Textiles',
		charterer: 'India Shipping Ltd.',
		info: 'ETA: Mumbai, IN / 04 Mar - 13:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '-22,480.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_010',
		longitude: -117.1611, // Near Los Angeles
		latitude: 32.7157,
		position: 0,
		status: 'anchored',
		vessel: 'mv Pacific Horizon',
		voyageId: 'VOY-MF-10',
		nextPort: 'San Francisco, US',
		eta: '07 Mar - 15:00',
		cargo: '2,200.000 mts - Consumer Goods',
		charterer: 'West Coast Logistics Inc.',
		info: 'ATA: Los Angeles, US / 05 Mar - 10:00',
		alert: true,
		urgent: false,
		pnl: {
			amount: '37,900.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_011',
		longitude: -3.7038, // Gibraltar Strait
		latitude: 35.8883,
		position: 90,
		status: 'enroute',
		vessel: 'mv Mediterranean Falcon',
		voyageId: 'VOY-MF-11',
		nextPort: 'Gibraltar, GI',
		eta: '03 Mar - 18:00',
		cargo: '1,700.000 mts - Mixed Cargo',
		charterer: 'Euro Maritime Services',
		info: 'ETA: Gibraltar, GI / 03 Mar - 18:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '-15,620.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_012',
		longitude: 127.8456, // South Korea Strait
		latitude: 34.8876,
		position: 45,
		status: 'enroute',
		vessel: 'mv Korean Voyager',
		voyageId: 'VOY-MF-12',
		nextPort: 'Busan, KR',
		eta: '04 Mar - 20:00',
		cargo: '2,400.000 mts - Automobiles',
		charterer: 'Korean Auto Exports',
		info: 'ETA: Busan, KR / 04 Mar - 20:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '19,800.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_013',
		longitude: -48.5, // South Atlantic
		latitude: -25.9667,
		position: 225,
		status: 'enroute',
		vessel: 'mv Cape Runner',
		voyageId: 'VOY-MF-13',
		nextPort: 'Santos, BR',
		eta: '05 Mar - 16:00',
		cargo: '3,100.000 mts - Coffee',
		charterer: 'Brazil Exports Co.',
		info: 'ETA: Santos, BR / 05 Mar - 16:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '21,500.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_014',
		longitude: 12.3456, // Mediterranean Sea
		latitude: 35.6789,
		position: 270,
		status: 'enroute',
		vessel: 'mv Mediterranean Star',
		voyageId: 'VOY-MF-14',
		nextPort: 'Barcelona, ES',
		eta: '06 Mar - 12:00',
		cargo: '1,800.000 mts - Wine & Spirits',
		charterer: 'European Beverage Traders',
		info: 'ETA: Barcelona, ES / 06 Mar - 12:00',
		alert: false,
		urgent: true,
		pnl: {
			amount: '12,000.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_015',
		longitude: 174.7633, // Near New Zealand
		latitude: -36.8485,
		position: 270,
		status: 'enroute',
		vessel: 'mv Oceanic Wanderer',
		voyageId: 'VOY-MF-15',
		nextPort: 'Auckland, NZ',
		eta: '06 Mar - 14:00',
		cargo: '2,100.000 mts - Timber',
		charterer: 'New Zealand Timber Exports',
		info: 'ETA: Auckland, NZ / 06 Mar - 14:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '17,400.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_016',
		longitude: -35.4567, // Mid North Atlantic
		latitude: 28.7645,
		position: 315,
		status: 'enroute',
		vessel: 'mv Atlantic Challenger',
		voyageId: 'VOY-MF-16',
		nextPort: 'Miami, US',
		eta: '05 Mar - 19:00',
		cargo: '1,900.000 mts - Cargo',
		charterer: 'Atlantic Shipping Co.',
		info: 'ETA: Miami, US / 05 Mar - 19:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '11,200.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_017',
		longitude: -13.991430243306004, // Mid South Atlantic
		latitude: -37.69774007250804,
		position: 135,
		status: 'enroute',
		vessel: 'mv Atlantic Pioneer',
		voyageId: 'VOY-MF-17',
		nextPort: 'Cape Town, ZA',
		eta: '07 Mar - 08:00',
		cargo: '2,300.000 mts - Grain',
		charterer: 'South African Grain Exports',
		info: 'ETA: Cape Town, ZA / 07 Mar - 08:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '16,800.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_018',
		longitude: 115.2167, // Near Bali
		latitude: -8.65,
		position: 45,
		status: 'enroute',
		vessel: 'mv Eastern Spirit',
		voyageId: 'VOY-MF-18',
		nextPort: 'Denpasar, ID',
		eta: '08 Mar - 15:00',
		cargo: '2,500.000 mts - Cargo',
		charterer: 'Indonesian Shipping Co.',
		info: 'ETA: Denpasar, ID / 08 Mar - 15:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '23,600.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_019',
		longitude: 27.900944, // Black Sea
		latitude: 43.186004,
		position: 315,
		status: 'enroute',
		vessel: 'mv Black Sea Runner',
		voyageId: 'VOY-MF-19',
		nextPort: 'Varna, BG',
		eta: '09 Mar - 11:00',
		cargo: '2,200.000 mts - Cargo',
		charterer: 'Bulgarian Shipping Co.',
		info: 'ETA: Varna, BG / 09 Mar - 11:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '14,700.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_020',
		longitude: 165.45, // Central Pacific
		latitude: 15.78,
		position: 90,
		status: 'enroute',
		vessel: 'mv Pacific Trader',
		voyageId: 'VOY-MF-20',
		nextPort: 'Honolulu, US',
		eta: '12 Mar - 14:00',
		cargo: '1,800.000 mts - Cargo',
		charterer: 'American Shipping Co.',
		info: 'ETA: Honolulu, US / 12 Mar - 14:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '10,500.00',
			currency: 'USD'
		}
	},
	{
		id: 'vessel_021',
		longitude: -145.23, // North Pacific
		latitude: 35.42,
		position: 270,
		status: 'enroute',
		vessel: 'mv Ocean Voyager',
		voyageId: 'VOY-MF-21',
		nextPort: 'Vancouver, CA',
		eta: '15 Mar - 09:00',
		cargo: '2,700.000 mts - Timber',
		charterer: 'Canadian Lumber Inc.',
		info: 'ETA: Vancouver, CA / 15 Mar - 09:00',
		alert: false,
		urgent: false,
		pnl: {
			amount: '20,300.00',
			currency: 'USD'
		}
	}
];

// Route data default
export const routeData = {
	type: 'FeatureCollection',
	features: [
		{
			type: 'Feature',
			properties: {},
			geometry: {
				type: 'LineString',
				coordinates: [
					[-70.21415418085773, -22.084668474090208],
					[-71.39057529056501, -24.980588012503482],
					[-71.7891463121313, -29.03531261644348],
					[-71.6227406316313, -33.01493421832621],
					[-74.04557621780754, -36.6551415605868],
					[-77.44837496289779, -51.51159898811477],
					[-73.83377991304961, -52.94710134496501],
					[-71.03103404250868, -54.01733338969794],
					[-70.76620766104026, -53.11962546757201],
					[-70.32483035859288, -52.693707722567666],
					[-69.8724186235843, -52.66694871838879],
					[-69.29862813040269, -52.38498849817082],
					[-58.42009054407481, -52.321970827747656],
					[-13.991430243306004, -37.69774007250804],
					[26.23769034136382, -34.28411976375421],
					[30.311034909127436, -31.130629821401875],
					[31.096971422557594, -29.85949354233595]
				]
			}
		}
	]
};
// Route data voyage
export const routeDataVoyage = {
	type: 'FeatureCollection',
	features: [
		{
			type: 'Feature',
			properties: {},
			geometry: {
				type: 'LineString',
				coordinates: [
					[-72.13, -17],
					[-70.23, -22.1],
					[-71, -23.5],
					[-71.7, -33],
					[-75, -40],
					[-76, -50],
					[-75, -53],
					[-71.5, -56],
					[-65, -56],
					[-56, -52],
					[-14.5, -38],
					[17.8, -33.1],
					[18.5, -35],
					[24, -35],
					[27, -34],
					[28.5, -33],
					[30.5, -31],
					[31.1, -29.9]
				]
			}
		}
	]
};

// Route styles
export const routeLayer: LayerProps = {
	id: 'route',
	type: 'line',
	layout: {
		'line-join': 'round',
		'line-cap': 'round'
	},
	paint: {
		'line-color': '#ffb900',
		'line-width': 1.5,
		'line-dasharray': [2, 6]
	}
};

export const routeVoyageLayer: LayerProps = {
	id: 'route',
	type: 'line',
	layout: {
		'line-join': 'round',
		'line-cap': 'round'
	},
	paint: {
		'line-color': '#ffb900',
		'line-width': 8,
		'line-opacity': 1,
		'line-border-width': 4,
		'line-border-color': '#0c1221'
	}
};

// Ports styles
export const portLayer: LayerProps = {
	id: 'port-points',
	type: 'circle',
	paint: {
		'circle-radius': 6,
		'circle-color': '#007cbf',
		'circle-stroke-width': 2,
		'circle-stroke-color': '#ffffff'
	}
};

export const portVoyageLayer: LayerProps = {
	id: 'port-points',
	type: 'symbol',
	layout: {
		'icon-image': 'port-icon', // This will reference the image we'll add to the map
		'icon-size': 0.75,
		'icon-allow-overlap': true,
		'icon-anchor': 'bottom'
	}
};

// Ports data default
export const portPoints = {
	type: 'FeatureCollection',
	features: [
		{
			type: 'Feature',
			properties: {
				name: 'Some Port',
				type: 'origin'
			},
			geometry: {
				type: 'Point',
				coordinates: [-70.21415418085773, -22.084668474090208]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Some Port',
				type: 'destination'
			},
			geometry: {
				type: 'Point',
				coordinates: [-71.6227406316313, -33.01493421832621]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Some Port',
				type: 'destination'
			},
			geometry: {
				type: 'Point',
				coordinates: [-77.44837496289779, -51.51159898811477]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Some Port',
				type: 'destination'
			},
			geometry: {
				type: 'Point',
				coordinates: [31.096971422557594, -29.85949354233595]
			}
		}
	]
};

// Ports data voyage
export const portPointsVoyage = {
	type: 'FeatureCollection',
	features: [
		{
			type: 'Feature',
			properties: {
				name: 'Matarani',
				type: 'origin'
			},
			geometry: {
				type: 'Point',
				coordinates: [-72.13, -17]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Tocopilla',
				type: 'origin'
			},
			geometry: {
				type: 'Point',
				coordinates: [-70.23, -22.1]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Valparaiso',
				type: 'origin'
			},
			geometry: {
				type: 'Point',
				coordinates: [-71.6, -33]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Saldanha Bay',
				type: 'origin'
			},
			geometry: {
				type: 'Point',
				coordinates: [18, -33]
			}
		},
		{
			type: 'Feature',
			properties: {
				name: 'Durban',
				type: 'destination'
			},
			geometry: {
				type: 'Point',
				coordinates: [31.1, -29.9]
			}
		}
	]
};
