// Example Node.js/Express proxy endpoint
// This would go in your backend server

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());

app.get('/api/itinerary', async (req, res) => {
  try {
    const response = await fetch('https://storage.googleapis.com/abx-workflows/199e1f1cbca973ea/itinerary.json');
    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch itinerary data' });
  }
});

app.listen(3001, () => {
  console.log('Proxy server running on port 3001');
});

// Then in your React component, you would fetch from:
// fetch('/api/itinerary') instead of the Google Storage URL
